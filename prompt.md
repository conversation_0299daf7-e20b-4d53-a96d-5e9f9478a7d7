
# Ford Access Alert Portal - Especificaciones Completas del Proyecto (Neon MCP)

## 📋 Descripción General del Proyecto

El **Ford Access Alert Portal** es una plataforma web moderna desarrollada específicamente para **MSX International Valencia HUB**, diseñada para la gestión integral de solicitudes de acceso, tickets de soporte y activos IT del Contact Center.

### Características Principales
- **Portal público** para solicitudes de acceso y creación de tickets
- **Dashboard administrativo** avanzado con gestión completa
- **Sistema de tiempo real** con notificaciones automáticas vía WebSockets
- **Gestión de inventario IT** completa
- **Sistema de usuarios** con roles diferenciados y autenticación custom
- **Reportes y analytics** en tiempo real

---

## 🛠️ Arquitectura Técnica Completa

### Frontend
```yaml
Framework: React 18 + TypeScript
Build Tool: Vite
Styling: Tailwind CSS
Components: shadcn/ui
Animations: Framer Motion
State Management: TanStack React Query
Routing: React Router DOM v6
Icons: Lucide React
Charts: Recharts
Notifications: Sonner
```

### Backend y Base de Datos
```yaml
Database: Neon PostgreSQL (Serverless)
Backend API: Express.js + Node.js
Authentication: Custom JWT + bcrypt
Real-time: Socket.io WebSockets
Security: Custom middleware + Rate limiting
File Storage: Cloudinary o AWS S3
API Client: Axios
```

### Herramientas de Desarrollo
```yaml
Type Checking: TypeScript
Linting: ESLint
Formatting: Prettier
Package Manager: npm
Git: Control de versiones
Database Client: pg (node-postgres)
Environment: dotenv
```

---

## 🎨 Especificaciones de UI/UX y Diseño

### Paleta de Colores Corporativa
```css
/* Colores Principales MSX International */
Primary Blue: #2563eb, #1d4ed8, #1e40af
Secondary Orange: #ea580c, #f97316, #fb923c
Success Green: #059669, #10b981, #34d399
Warning Orange: #d97706, #f59e0b, #fbbf24
Error Red: #dc2626, #ef4444, #f87171
Neutral Grays: #111827, #374151, #6b7280, #9ca3af, #d1d5db, #f3f4f6

/* Gradientes */
Blue Gradient: from-blue-50 via-white to-gray-100
Orange Gradient: from-orange-500 to-orange-600
```

### Tipografía
```css
Font Family: Inter, system-ui, sans-serif
Headings: 
  - h1: text-4xl font-bold (2.25rem)
  - h2: text-2xl font-bold (1.5rem) 
  - h3: text-xl font-bold (1.25rem)
Body Text: text-base (1rem)
Small Text: text-sm (0.875rem)
```

### Componentes de Diseño
```yaml
Cards: Rounded corners (rounded-lg), subtle shadows, border gradients
Buttons: 
  - Primary: Blue gradient with hover effects
  - Secondary: Orange gradient 
  - Outline: Border with hover fill
Forms: Clean inputs with focus states, validation styling
Tables: Zebra striping, hover effects, responsive design
Badges: Color-coded by status/priority
Modals: Backdrop blur, smooth animations
```

### Animaciones y Efectos
```yaml
Page Transitions: Framer Motion fade-in, slide-up
Hover Effects: Scale transforms, color transitions
Loading States: Skeleton loaders, spinner animations
Real-time Updates: Pulse animations for new items
Notification Toasts: Slide-in from top-right
```

---

## 🗄️ Estructura de Base de Datos Detallada

### Configuración de Neon PostgreSQL
```yaml
Connection String: postgresql://[user]:[password]@[host]/[database]?sslmode=require
SSL Mode: Required
Connection Pooling: Enabled
Max Connections: 100
Auto-scaling: Enabled
```

### Tablas Principales

#### 1. `tickets`
```sql
CREATE TABLE tickets (
  id TEXT PRIMARY KEY, -- Format: TK-XXXXXX
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT NOT NULL,
  priority TEXT CHECK (priority IN ('low', 'medium', 'high', 'critical')) DEFAULT 'medium',
  status TEXT CHECK (status IN ('pending', 'open', 'in_progress', 'resolved', 'closed')) DEFAULT 'pending',
  creator_id UUID,
  creator_email TEXT NOT NULL,
  submitter_cdsid TEXT NOT NULL, -- Campo obligatorio único
  submitter_email TEXT,
  affected_cdsid TEXT,
  ticket_number SERIAL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  closed_at TIMESTAMPTZ,
  resolution_time INTEGER, -- Segundos
  sla_due_date TIMESTAMPTZ,
  comentarios JSONB DEFAULT '[]'::jsonb
);
```

#### 2. `notificaciones_acceso` (Solicitudes de Acceso)
```sql
CREATE TABLE notificaciones_acceso (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  empleado_nombre TEXT NOT NULL,
  empleado_cdsid TEXT NOT NULL,
  region TEXT NOT NULL,
  plataformas_faltantes TEXT[] NOT NULL,
  descripcion TEXT NOT NULL,
  prioridad TEXT CHECK (prioridad IN ('baja', 'media', 'alta', 'critica')) DEFAULT 'media',
  estado TEXT CHECK (estado IN ('pendiente', 'en_proceso', 'completado', 'rechazado')) DEFAULT 'pendiente',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  assigned_to TEXT,
  resolved_at TIMESTAMPTZ,
  tags TEXT[]
);
```

#### 3. `asset_items` (Inventario IT)
```sql
CREATE TABLE asset_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  collectionid UUID NOT NULL,
  tipo TEXT NOT NULL,
  serial TEXT,
  modelo TEXT,
  estado TEXT NOT NULL,
  notas TEXT
);
```

#### 4. `admin_users` (Usuarios Administrativos)
```sql
CREATE TABLE admin_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  password_hash TEXT NOT NULL, -- bcrypt hash
  role TEXT CHECK (role IN ('superadmin', 'admin', 'viewer')) DEFAULT 'admin',
  active BOOLEAN DEFAULT true,
  last_login TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Funciones de Base de Datos
```sql
-- Función para generar números de ticket automáticos
CREATE OR REPLACE FUNCTION generate_ticket_number()
RETURNS TEXT AS $$
DECLARE
    next_num INTEGER;
    ticket_num TEXT;
BEGIN
    SELECT COALESCE(MAX(CAST(SUBSTRING(id FROM 4) AS INTEGER)), 0) + 1 
    INTO next_num 
    FROM tickets 
    WHERE id LIKE 'TK-%';
    
    ticket_num := 'TK-' || LPAD(next_num::TEXT, 6, '0');
    RETURN ticket_num;
END;
$$ LANGUAGE plpgsql;

-- Trigger para establecer SLA automáticamente
CREATE OR REPLACE FUNCTION set_sla_due_date()
RETURNS TRIGGER AS $$
BEGIN
    CASE NEW.priority
        WHEN 'critical' THEN NEW.sla_due_date := NEW.created_at + INTERVAL '4 hours';
        WHEN 'high' THEN NEW.sla_due_date := NEW.created_at + INTERVAL '24 hours';
        WHEN 'medium' THEN NEW.sla_due_date := NEW.created_at + INTERVAL '72 hours';
        WHEN 'low' THEN NEW.sla_due_date := NEW.created_at + INTERVAL '1 week';
    END CASE;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

---

## 📱 Funcionalidades por Módulos

### 1. Portal Público (/) 
```yaml
Características:
  - Landing page con branding MSX International
  - Estadísticas del sistema en tiempo real
  - Enlaces rápidos a funcionalidades principales
  - Información corporativa y de contacto

Componentes:
  - Header profesional con logos
  - Tarjetas de estadísticas en tiempo real
  - Botones de acción rápida
  - Footer corporativo
```

### 2. Solicitud de Accesos (/access-request)
```yaml
Características:
  - Formulario intuitivo para solicitar accesos
  - Validación de CDSID y email corporativo
  - Selección múltiple de plataformas
  - Descripción detallada de necesidades

Validaciones:
  - CDSID obligatorio (formato específico)
  - Email corporativo (@msxi.com, @ford.com)
  - Campos requeridos claramente marcados
  - Feedback inmediato de validación
```

### 3. Sistema de Tickets (/tickets)
```yaml
Características:
  - Creación simplificada de tickets (solo CDSID obligatorio)
  - Vista pública de tickets existentes
  - Sistema de categorías predefinidas
  - Seguimiento de estado sin información sensible

Funciones:
  - Crear nuevo ticket
  - Buscar tickets por número o CDSID
  - Ver detalles públicos del ticket
  - Estado y progreso visual
```

### 4. Dashboard Administrativo (/admin-dashboard)
```yaml
Características:
  - Gestión completa de tickets con comentarios
  - Panel de actividad reciente en tiempo real
  - Estadísticas avanzadas y métricas
  - Gestión de usuarios administrativos
  - Inventario de activos IT completo
  - Sistema de reportes y analytics

Módulos:
  - Resumen: Estadísticas en tiempo real
  - Tickets: Gestión avanzada con comentarios
  - Inventario: CRUD completo de activos
  - Usuarios: Administración de accesos
  - Solicitudes: Gestión de accesos (solo superadmin)
  - Reportes: Analytics y métricas
```

### 5. Autenticación Admin (/admin-login)
```yaml
Características:
  - Login seguro con JWT + bcrypt
  - Validación de credenciales
  - Gestión de roles (superadmin, admin, viewer)
  - Sesiones seguras con refresh tokens

Seguridad:
  - Contraseñas hasheadas con bcrypt
  - Roles diferenciados con permisos específicos
  - JWT con expiración automática
  - Protección contra ataques de fuerza bruta
```

---

## ⚡ Funcionalidades de Tiempo Real

### WebSockets con Socket.io
```typescript
// Configuración del cliente Socket.io
import io from 'socket.io-client';

const socket = io(process.env.REACT_APP_WEBSOCKET_URL, {
  auth: {
    token: localStorage.getItem('auth_token')
  }
});

// Escuchar nuevos tickets
socket.on('new_ticket', (data) => {
  showNotification('Nuevo ticket recibido');
  updateStats();
});

// Escuchar actualizaciones de estado
socket.on('ticket_updated', (data) => {
  showNotification('Ticket actualizado');
  refetchData();
});
```

### Notificaciones Toast
```yaml
Tipos:
  - Nuevos tickets: 8 segundos duración
  - Actualizaciones: 6 segundos duración
  - Errores: Persistente hasta cierre manual
  - Éxito: 4 segundos duración

Contexto:
  - Iconos específicos por tipo
  - Colores diferenciados
  - Información detallada del evento
```

### Panel de Actividad Reciente
```yaml
Características:
  - Últimos 5 eventos del sistema
  - Diferenciación entre creación y actualización
  - Badges de prioridad y estado
  - Timestamps en español
  - Actualización automática cada 30 segundos
```

---

## 🔒 Sistema de Seguridad

### Autenticación JWT Personalizada
```yaml
Sistema Público:
  - Sin autenticación requerida
  - Acceso limitado a funciones públicas
  - Validación de datos en frontend

Sistema Administrativo:
  - Autenticación JWT personalizada
  - Refresh tokens para sesiones largas
  - Roles diferenciados (superadmin, admin, viewer)
  - Middleware de autorización en API
```

### Middleware de Seguridad
```javascript
// Middleware de autenticación
const authMiddleware = (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'Token requerido' });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ error: 'Token inválido' });
  }
};

// Middleware de roles
const roleMiddleware = (roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Permisos insuficientes' });
    }
    next();
  };
};
```

### Permisos por Rol
```yaml
Viewer:
  - Ver tickets y estadísticas
  - Acceso de solo lectura

Admin:
  - Gestión completa de tickets
  - Inventario y usuarios
  - Notificaciones y reportes

Superadmin:
  - Todos los permisos de Admin
  - Gestión de solicitudes de acceso
  - Configuración del sistema
```

---

## 📊 Métricas y Analytics

### Estadísticas en Tiempo Real
```yaml
Dashboard Principal:
  - Total de tickets (con división por estado)
  - Usuarios administrativos activos
  - Solicitudes de acceso pendientes
  - Tickets críticos

Métricas Avanzadas:
  - Tiempo promedio de resolución
  - Distribución por prioridad
  - Tendencias temporales
  - Rendimiento por categoría
```

### Reportes Disponibles
```yaml
Tickets:
  - Volumen diario/semanal/mensual
  - Tiempo de resolución por prioridad
  - Distribución por categoría
  - SLA compliance

Accesos:
  - Solicitudes por región
  - Tiempo de aprobación
  - Plataformas más solicitadas
  - Tendencias de crecimiento
```

---

## 🚀 Configuración de Desarrollo

### Variables de Entorno
```env
# Database
DATABASE_URL=postgresql://[user]:[password]@[host]/[database]?sslmode=require
DB_HOST=your-neon-host
DB_NAME=your-database-name
DB_USER=your-username
DB_PASSWORD=your-password
DB_PORT=5432

# Authentication
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-refresh-secret
JWT_EXPIRE=24h
JWT_REFRESH_EXPIRE=7d

# Server
PORT=3001
NODE_ENV=development

# WebSocket
WEBSOCKET_PORT=3002

# Frontend
REACT_APP_API_URL=http://localhost:3001
REACT_APP_WEBSOCKET_URL=http://localhost:3002

# File Upload (opcional)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

### Scripts de Package.json
```json
{
  "scripts": {
    "dev": "concurrently \"npm run server\" \"npm run client\"",
    "server": "nodemon server/index.js",
    "client": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "migrate": "node server/migrations/run.js"
  }
}
```

### Dependencias Principales
```json
{
  "dependencies": {
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "react-router-dom": "^6.26.2",
    "@tanstack/react-query": "^5.56.2",
    "tailwindcss": "latest",
    "framer-motion": "^12.18.1",
    "lucide-react": "^0.462.0",
    "recharts": "^2.12.7",
    "sonner": "^1.5.0",
    "axios": "^1.6.0",
    "socket.io-client": "^4.7.5"
  },
  "devDependencies": {
    "express": "^4.18.2",
    "socket.io": "^4.7.5",
    "pg": "^8.11.3",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.2",
    "cors": "^2.8.5",
    "helmet": "^7.1.0",
    "express-rate-limit": "^7.1.5",
    "dotenv": "^16.3.1",
    "nodemon": "^3.0.2",
    "concurrently": "^8.2.2"
  }
}
```

---

## 📦 Instrucciones de Despliegue

### Preparación del Proyecto
```bash
# 1. Crear nuevo proyecto React con Vite
npm create vite@latest ford-access-portal -- --template react-ts
cd ford-access-portal

# 2. Instalar dependencias frontend
npm install @tanstack/react-query react-router-dom framer-motion
npm install lucide-react tailwindcss @tailwindcss/forms
npm install recharts sonner date-fns clsx tailwind-merge
npm install axios socket.io-client
npm install @radix-ui/react-* (todos los componentes shadcn/ui necesarios)

# 3. Instalar dependencias backend
npm install express socket.io pg bcryptjs jsonwebtoken
npm install cors helmet express-rate-limit dotenv
npm install --save-dev nodemon concurrently

# 4. Configurar Tailwind CSS
npx tailwindcss init -p
```

### Configuración de Neon PostgreSQL
```yaml
Pasos:
  1. Crear proyecto en Neon Console
  2. Obtener connection string
  3. Configurar variables de entorno
  4. Ejecutar migraciones SQL
  5. Crear usuarios administrativos iniciales
  6. Configurar índices para performance
```

### Estructura de Carpetas
```
ford-access-portal/
├── src/                     # Frontend React
│   ├── components/          # Componentes reutilizables
│   ├── pages/              # Páginas principales
│   ├── hooks/              # Custom hooks
│   ├── contexts/           # Context providers
│   ├── lib/                # Utilidades y configuración
│   └── styles/             # Estilos globales
├── server/                  # Backend Node.js
│   ├── routes/             # Rutas de API
│   ├── middleware/         # Middleware personalizado
│   ├── models/             # Modelos de datos
│   ├── services/           # Servicios de negocio
│   ├── utils/              # Utilidades del servidor
│   ├── migrations/         # Migraciones de BD
│   └── socket/             # Configuración WebSocket
└── public/                 # Archivos estáticos
```

---

## ⭐ Características Únicas y Diferenciadores

### 1. Sistema Híbrido de Autenticación
```yaml
Característica Única:
  - Portal público sin autenticación
  - Sistema administrativo con JWT personalizado
  - Roles diferenciados con permisos granulares
  - Refresh tokens para sesiones largas
```

### 2. Formularios Simplificados
```yaml
Innovación:
  - Solo CDSID obligatorio para tickets
  - Validación inteligente de emails corporativos
  - UX optimizada para Contact Center
```

### 3. Real-time Dashboard con WebSockets
```yaml
Funcionalidad Avanzada:
  - Panel de actividad reciente automático
  - Diferenciación inteligente entre creación/actualización
  - Notificaciones contextuales específicas
  - Conexión persistente con reconexión automática
```

### 4. Gestión de Inventario Integrada
```yaml
Característica Completa:
  - CRUD completo de activos IT
  - Historial de movimientos
  - Estados y categorías personalizables
```

### 5. Sistema de Tickets Profesional
```yaml
Funcionalidades Avanzadas:
  - Numeración automática (TK-XXXXXX)
  - SLA automático por prioridad
  - Sistema de comentarios integrado
  - Estados de workflow completos
```

---

## 🎯 Instrucciones de Implementación

### Orden de Desarrollo Recomendado
```yaml
Fase 1 - Configuración Base:
  1. Configurar proyecto Vite + React + TypeScript
  2. Instalar y configurar Tailwind CSS
  3. Configurar Neon PostgreSQL y variables de entorno
  4. Crear estructura de carpetas base
  5. Configurar Express.js server básico

Fase 2 - Base de Datos y API:
  1. Crear conexión a Neon PostgreSQL
  2. Implementar migraciones SQL
  3. Crear modelos de datos
  4. Implementar rutas básicas de API
  5. Configurar middleware de seguridad

Fase 3 - Autenticación y Autorización:
  1. Implementar sistema JWT
  2. Crear middleware de autenticación
  3. Implementar roles y permisos
  4. Configurar rutas protegidas

Fase 4 - Componentes UI y Funcionalidades Core:
  1. Implementar componentes shadcn/ui
  2. Crear componentes de diseño base
  3. Portal público con formularios
  4. Sistema de tickets básico
  5. Dashboard administrativo base

Fase 5 - Funcionalidades Avanzadas:
  1. Configurar Socket.io para tiempo real
  2. Sistema de notificaciones
  3. Gestión de inventario
  4. Reportes y analytics

Fase 6 - Optimización y Despliegue:
  1. Performance optimization
  2. Error handling robusto
  3. Testing y validación
  4. Configuración de producción
  5. Documentación final
```

### Puntos Críticos de Implementación
```yaml
Base de Datos:
  - Configurar connection pooling en Neon
  - Implementar migraciones incrementales
  - Crear índices para queries frecuentes

API Backend:
  - Implementar rate limiting
  - Configurar CORS correctamente
  - Validación robusta de datos
  - Error handling centralizado

Autenticación:
  - Implementar refresh token rotation
  - Configurar expiración de sesiones
  - Proteger rutas sensibles
  - Logging de accesos

WebSockets:
  - Configurar namespaces por funcionalidad
  - Implementar reconexión automática
  - Manejo de desconexiones inesperadas
  - Autenticación de conexiones

Frontend:
  - Responsive design completo
  - Estados de loading apropiados
  - Error boundaries para componentes
  - Optimización de re-renders
```

---

**Este prompt.md contiene todas las especificaciones necesarias para recrear exactamente el Ford Access Alert Portal usando Neon PostgreSQL como base de datos en cualquier nuevo proyecto. Incluye arquitectura técnica, diseño visual, estructura de datos, funcionalidades específicas y instrucciones detalladas de implementación.**
