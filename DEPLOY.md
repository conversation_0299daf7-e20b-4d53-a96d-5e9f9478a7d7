# 🚀 Ford Access Alert Portal - Deploy Guide

## 📋 Resumen del Proyecto

**Ford Access Alert Portal** es una aplicación web moderna para la gestión de accesos y tickets de soporte en MSX International. Incluye:

- ✅ **Panel de administración** completo con animaciones
- ✅ **Sistema de tickets** para solicitudes de acceso
- ✅ **Gestión de usuarios** con CRUD completo
- ✅ **Branding MSX** con diseño corporativo
- ✅ **Integración Supabase** para base de datos
- ✅ **Autenticación** segura con roles

## 🔧 Tecnologías Utilizadas

- **Frontend:** React + TypeScript + Vite
- **UI:** Tailwind CSS + Shadcn/ui + Framer Motion
- **Base de datos:** Supabase (PostgreSQL)
- **Autenticación:** Supabase Auth
- **Deploy:** Netlify

## 🌐 Deploy en Netlify

### Opción 1: Deploy Manual (Recomendado)

1. **Preparar el build:**
   ```bash
   npm install
   npm run build
   ```

2. **Subir a Netlify:**
   - Ve a [netlify.com](https://netlify.com)
   - Arrastra la carpeta `dist` al área de deploy
   - O usa Netlify CLI: `netlify deploy --prod --dir=dist`

### Opción 2: Deploy desde Git

1. **Conectar repositorio:**
   - Conecta tu repositorio de GitHub
   - Build command: `npm run build`
   - Publish directory: `dist`

### Opción 3: Netlify CLI

1. **Instalar Netlify CLI:**
   ```bash
   npm install -g netlify-cli
   ```

2. **Login y deploy:**
   ```bash
   netlify login
   netlify init
   netlify deploy --prod --dir=dist
   ```

## ⚙️ Variables de Entorno

Configura estas variables en Netlify:

```env
VITE_SUPABASE_URL=https://ffxtpwgrkvicknkjvzgo.supabase.co
VITE_SUPABASE_ANON_KEY=tu_supabase_anon_key
```

## 🔗 URLs del Proyecto

- **Repositorio:** https://github.com/stoja88/ford-access-alert-portal
- **Supabase Project:** ffxtpwgrkvicknkjvzgo
- **Demo Local:** http://localhost:8080

## 👤 Credenciales de Prueba

- **Email:** <EMAIL>
- **Password:** password123

## 📁 Estructura del Proyecto

```
ford-access-alert-portal/
├── src/
│   ├── components/          # Componentes React
│   ├── pages/              # Páginas principales
│   ├── contexts/           # Context providers
│   ├── hooks/              # Custom hooks
│   ├── integrations/       # Supabase config
│   └── lib/                # Utilidades
├── public/                 # Assets estáticos
├── dist/                   # Build de producción
├── netlify.toml           # Configuración Netlify
└── package.json           # Dependencias
```

## 🎯 Funcionalidades Principales

### 🏠 **Página Principal**
- Diseño animado con branding MSX
- Formulario de solicitud de acceso
- Información corporativa

### 🔐 **Admin Login**
- Interfaz mejorada con animaciones 3D
- Gradientes corporativos
- Validación segura

### 📊 **Dashboard Administrativo**
- Panel de estadísticas en tiempo real
- Gestión de tickets y solicitudes
- Actualizaciones recientes con gestión manual

### 🎫 **Sistema de Tickets**
- Estados: solicitada → reclamada → con_acceso_pendiente_verificar → verificado → cerrada
- Flujo de trabajo completo
- Notificaciones en tiempo real

### 👥 **Gestión de Usuarios**
- CRUD completo con animaciones
- Filtros avanzados y búsqueda
- Exportación a CSV
- Roles y permisos

## 🔧 Comandos Útiles

```bash
# Desarrollo
npm run dev

# Build
npm run build

# Preview build
npm run preview

# Lint
npm run lint
```

## 📞 Soporte

Para soporte técnico, contacta al equipo de desarrollo de MSX International.

---

**© 2025 MSX International - Valencia HUB**
*powered by Karedesk*
