# Ford Access Alert Portal

## 🚀 Portal de Gestión de Accesos y Servicios IT para MSX International

Portal web moderno desarrollado por **Karedesk** para la gestión integral de solicitudes de acceso, tickets de soporte y activos IT de **MSX International**.

## 🆕 Últimas Actualizaciones (Diciembre 2024)

### ✨ Panel de Actividad Reciente en Tiempo Real
- **Dashboard de actualizaciones** integrado en el resumen del sistema
- **Detección completa** de creaciones y actualizaciones de tickets
- **Visualización diferenciada** con iconos específicos (nuevos vs actualizados)
- **Información detallada** con prioridad, estado y timestamps
- **Animaciones progresivas** para mejor experiencia visual

### 🔔 Sistema de Notificaciones Mejorado
- **Toast notifications inteligentes** para diferentes tipos de eventos
- **Notificaciones específicas** para cambios de estado y prioridad
- **Alertas en tiempo real** con duración optimizada
- **Detección automática** de qué campos fueron modificados

### 📊 Mejoras en el Dashboard Administrativo
- **Suscripciones completas** a eventos INSERT y UPDATE de Supabase
- **Estadísticas en tiempo real** con datos actualizados automáticamente
- **Panel de actividad** que muestra los últimos 5 eventos del sistema
- **Badges informativos** con colores diferenciados por prioridad y estado

### 🎯 Funcionalidades Previas
- **Simplificación del formulario de tickets**: Solo CDSID obligatorio
- **Mejora de validación de emails**: Acepta dominios @msxi.com y @ford.com
- **Integración total con Supabase**: Todos los componentes con datos reales

## 📋 Características Principales

### 🏠 Portal Público
- **Página de inicio** con branding profesional MSX International
- **Solicitud de accesos** a plataformas y sistemas
- **Creación simplificada de tickets** con solo CDSID obligatorio
- **Múltiples dominios de correo** (@msxi.com y @ford.com)
- **Seguimiento público** de tickets sin estadísticas sensibles
- **Diseño responsive** y moderno con Tailwind CSS

### 🔐 Dashboard Administrativo
- **Panel de actividad reciente** con eventos en tiempo real
- **Gestión completa de solicitudes de acceso** en tiempo real
- **Sistema avanzado de tickets** con CRUD completo y comentarios
- **Inventario de activos IT** con gestión de equipos
- **Estadísticas dinámicas** conectadas a Supabase con auto-refresh
- **Notificaciones y alertas** automáticas diferenciadas
- **Filtros avanzados** y búsqueda inteligente

### 🎨 Características de UI/UX
- **Animaciones fluidas** con Framer Motion y entrada progresiva
- **Componentes modernos** con shadcn/ui y gradientes
- **Tema consistente** con colores corporativos MSX
- **Notificaciones toast** inteligentes con contexto específico
- **Diseño responsive** para todos los dispositivos
- **Iconografía contextual** para diferentes tipos de actividad

## 🛠️ Tecnologías Utilizadas

### Frontend
- **React 18** con TypeScript
- **Vite** para desarrollo y build optimizado
- **Tailwind CSS** para estilos modernos
- **shadcn/ui** para componentes de interfaz
- **Framer Motion** para animaciones
- **React Query** para gestión de estado y cache
- **React Router** para navegación

### Backend & Base de Datos
- **Supabase** como backend completo
- **PostgreSQL** para base de datos
- **Real-time subscriptions** para actualizaciones en vivo
- **Row Level Security (RLS)** para seguridad de datos

### Herramientas de Desarrollo
- **ESLint** para calidad de código
- **Prettier** para formateo automático
- **Git** para control de versiones
- **npm** para gestión de dependencias

## 📊 Estructura de Datos

### Tablas Principales
- **`tickets`** - Gestión de tickets de soporte
- **`notificaciones_acceso`** - Solicitudes de acceso a sistemas
- **`asset_items`** - Inventario de activos IT

### Campos de Solicitudes de Acceso
```typescript
interface AccessRequest {
  id: string;
  empleado_nombre: string;
  empleado_cdsid: string;
  region: string;
  plataformas_faltantes: string[];
  descripcion: string;
  prioridad: 'baja' | 'media' | 'alta' | 'critica';
  estado: 'pendiente' | 'en_proceso' | 'completado' | 'rechazado';
  created_at: string;
  updated_at: string;
}
```

### Campos de Tickets
```typescript
interface Ticket {
  id: string; // UUID
  title: string;
  description: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'open' | 'in_progress' | 'resolved' | 'closed';
  creator_id: string; // UUID reference
  creator_email: string;
  submitter_cdsid: string; // CDSID (único campo obligatorio en la creación)
  submitter_email: string | null;
  affected_cdsid: string | null;
  ticket_number: number; // Auto-incrementing
  created_at: string;
  updated_at: string;
  closed_at: string | null;
}
```

### Campos de Activos IT
```typescript
interface AssetItem {
  id: string;
  collectionid: string;
  tipo: string;
  serial?: string;
  modelo?: string;
  estado: string;
  notas?: string;
}
```

## 🚀 Instalación y Configuración

### Prerrequisitos
- Node.js 18+ y npm
- Cuenta de Supabase configurada

### Pasos de Instalación

1. **Clonar el repositorio**
```bash
git clone https://github.com/stoja88/ford-access-alert-portal.git
cd ford-access-alert-portal
```

2. **Instalar dependencias**
```bash
npm install
```

3. **Configurar variables de entorno**
```bash
# Crear archivo .env.local
VITE_SUPABASE_URL=tu_supabase_url
VITE_SUPABASE_ANON_KEY=tu_supabase_anon_key
```

4. **Iniciar servidor de desarrollo**
```bash
npm run dev
```

5. **Acceder a la aplicación**
- Portal público: `http://localhost:5173`
- Dashboard admin: `http://localhost:5173/admin`

## 📱 Uso del Sistema

### Para Usuarios Finales (MSX International)
1. **Acceder al portal** en la URL principal
2. **Solicitar accesos** completando el formulario
3. **Crear tickets** de soporte cuando sea necesario
4. **Hacer seguimiento** del estado de solicitudes

### Para Administradores
1. **Iniciar sesión** en `/admin`
2. **Gestionar solicitudes** desde el dashboard
3. **Administrar tickets** con herramientas avanzadas
4. **Controlar inventario** de activos IT
5. **Monitorear estadísticas** en tiempo real

## 🔧 Componentes Principales

### Gestión de Accesos
- **`ModernAccessManager`** - Gestión completa de solicitudes
- **`AccessRequestCard`** - Visualización de solicitudes individuales
- **`AccessRequestAlert`** - Notificaciones y alertas

### Gestión de Tickets
- **`AdvancedTicketManager`** - Sistema completo de tickets
- **`TicketCard`** - Tarjetas de tickets individuales

### Gestión de Activos
- **`AdvancedAssetManager`** - Inventario completo de activos IT
- **`AssetCard`** - Visualización de activos individuales

### Estadísticas y Monitoreo
- **`RealTimeStats`** - Dashboard de estadísticas en vivo
- **`MetricsCard`** - Tarjetas de métricas individuales

## 🔄 Panel de Actividad Reciente

### Características del Panel
- **Visualización en tiempo real** de los últimos 5 eventos del sistema
- **Detección inteligente** entre tickets nuevos y actualizados
- **Iconografía diferenciada**:
  - 🎫 **Icono azul** para tickets nuevos creados
  - 🔄 **Icono naranja** para tickets actualizados
- **Información completa** por evento:
  - Título del ticket
  - Tipo de acción (creado/actualizado)
  - Badge de prioridad con colores
  - Badge de estado actual
  - Timestamp en formato español

### Eventos Detectados
- ✅ **Creación de tickets** - Aparece inmediatamente con icono azul
- ✅ **Cambios de estado** - Pendiente → En Progreso → Cerrado
- ✅ **Cambios de prioridad** - Normal → Alta → Crítica
- ✅ **Actualizaciones generales** - Cualquier modificación del ticket
- ✅ **Asignaciones** - Cambios en responsable del ticket

### Notificaciones Toast Inteligentes
- **Tickets nuevos**: "🎫 Nuevo Ticket Recibido" (8 segundos)
- **Actualizaciones**: "🔄 Ticket Actualizado" (6 segundos)
- **Detección específica**: Muestra exactamente qué campo cambió
- **Mensajes contextuales**: "Estado cambiado a: En Progreso"

### Integración Técnica
- **Suscripciones Supabase**: Eventos INSERT y UPDATE en tiempo real
- **Ordenamiento inteligente**: Por `updated_at` para mostrar actividad reciente
- **Diferenciación temporal**: Lógica de 1 minuto para distinguir creación vs actualización
- **Performance optimizada**: Solo muestra los 5 eventos más recientes

## 🎯 Funcionalidades Avanzadas

### Real-time Updates
- **Actualizaciones automáticas** cuando se crean nuevas solicitudes
- **Notificaciones push** para administradores
- **Sincronización en tiempo real** entre usuarios

### Filtros y Búsqueda
- **Filtros por estado** (pendiente, en proceso, completado)
- **Filtros por prioridad** (baja, media, alta, crítica)
- **Búsqueda por texto** en múltiples campos
- **Filtros por fecha** y asignación

### Gestión de Estados
- **Workflow completo** de solicitudes y tickets
- **Transiciones de estado** controladas
- **Historial de cambios** automático

## 🔒 Seguridad

- **Autenticación** mediante Supabase Auth
- **Autorización** basada en roles
- **Row Level Security** en base de datos
- **Validación** de datos en frontend y backend

## 🌐 Despliegue

### Desarrollo
```bash
npm run dev
```

### Producción
```bash
npm run build
npm run preview
```

### Variables de Entorno Requeridas
```env
VITE_SUPABASE_URL=https://tu-proyecto.supabase.co
VITE_SUPABASE_ANON_KEY=tu_clave_anonima
```

## 📈 Roadmap y Mejoras Futuras

- [ ] Integración con sistemas externos de MSX
- [ ] Reportes avanzados y analytics
- [ ] Aplicación móvil nativa
- [ ] API REST para integraciones
- [ ] Sistema de aprobaciones multinivel
- [ ] Automatización de workflows

## 🤝 Contribución

1. Fork del proyecto
2. Crear rama para feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit de cambios (`git commit -m 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📞 Soporte

Para soporte técnico o consultas:
- **Desarrollado por:** Karedesk
- **Cliente:** MSX International
- **Repositorio:** [GitHub](https://github.com/stoja88/ford-access-alert-portal)

## 📄 Licencia

Este proyecto está desarrollado específicamente para MSX International por Karedesk.

---

**Desarrollado con ❤️ por Karedesk para MSX International**
