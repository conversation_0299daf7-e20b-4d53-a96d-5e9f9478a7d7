functionsDirectory = "/root/aug/ford-access-alert-portal/netlify/functions"
functionsDirectoryOrigin = "ui"
headersOrigin = "config"
redirectsOrigin = "config"
plugins = []

[functions]

[functions."*"]

[build]
publish = "/root/aug/ford-access-alert-portal/dist"
publishOrigin = "config"
commandOrigin = "config"
command = "npm run build"
functions = "/root/aug/ford-access-alert-portal/netlify/functions"

[build.environment]
NODE_VERSION = "18"

[build.processing]

[build.processing.css]

[build.processing.html]

[build.processing.images]

[build.processing.js]

[build.services]

[[headers]]
for = "/*"

[headers.values]
X-Frame-Options = "DENY"
X-XSS-Protection = "1; mode=block"
X-Content-Type-Options = "nosniff"
Referrer-Policy = "strict-origin-when-cross-origin"
Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[headers]]
for = "/assets/*"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*.html"

[headers.values]
Cache-Control = "public, max-age=3600"

[[redirects]]
from = "/*"
to = "/index.html"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]