import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ffxtpwgrkvicknkjvzgo.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZmeHRwd2dya3ZpY2tuamp2emdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0NjQ0MzcsImV4cCI6MjA1MDA0MDQzN30.Wd3Eo8Aw4sTdkKvqJKNZvJhqOyBKkZqhOjZJXdDfzKI';

const supabase = createClient(supabaseUrl, supabaseKey);

async function addSampleComments() {
  try {
    console.log('Obteniendo tickets existentes...');
    
    // Obtener algunos tickets existentes
    const { data: tickets, error: ticketsError } = await supabase
      .from('tickets')
      .select('id, title')
      .limit(3);
    
    if (ticketsError) {
      console.error('Error obteniendo tickets:', ticketsError);
      return;
    }
    
    if (!tickets || tickets.length === 0) {
      console.log('No hay tickets disponibles para agregar comentarios');
      return;
    }
    
    console.log(`Encontrados ${tickets.length} tickets`);
    
    // Agregar comentarios de ejemplo
    const sampleComments = [
      {
        content: "Ticket recibido, iniciando investigación del problema reportado.",
        author: "Técnico MSX",
        is_internal: false
      },
      {
        content: "Se identificó que el problema está relacionado con permisos de acceso.",
        author: "Admin Sistema",
        is_internal: true
      },
      {
        content: "Solución aplicada. Favor confirmar si el problema persiste.",
        author: "Soporte Técnico",
        is_internal: false
      }
    ];
    
    for (let i = 0; i < tickets.length && i < sampleComments.length; i++) {
      const ticket = tickets[i];
      const comment = sampleComments[i];
      
      console.log(`Agregando comentario al ticket: ${ticket.title}`);
      
      const { error } = await supabase
        .from('ticket_comments')
        .insert({
          ticket_id: ticket.id,
          content: comment.content,
          author: comment.author,
          is_internal: comment.is_internal
        });
      
      if (error) {
        console.error(`Error agregando comentario al ticket ${ticket.id}:`, error);
      } else {
        console.log(`✅ Comentario agregado al ticket: ${ticket.title}`);
      }
    }
    
    console.log('✅ Comentarios de ejemplo agregados exitosamente');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

addSampleComments();
