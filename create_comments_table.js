import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ffxtpwgrkvicknkjvzgo.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZmeHRwd2dya3ZpY2tuamp2emdvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0NjQ0MzcsImV4cCI6MjA1MDA0MDQzN30.Wd3Eo8Aw4sTdkKvqJKNZvJhqOyBKkZqhOjZJXdDfzKI';

const supabase = createClient(supabaseUrl, supabaseKey);

async function createCommentsTable() {
  try {
    console.log('Creando tabla ticket_comments...');
    
    // Primero verificar si la tabla ya existe
    const { data: tables, error: checkError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_name', 'ticket_comments')
      .eq('table_schema', 'public');
    
    if (checkError) {
      console.log('No se pudo verificar si la tabla existe, procediendo a crearla...');
    }
    
    if (tables && tables.length > 0) {
      console.log('✅ La tabla ticket_comments ya existe');
      return;
    }
    
    // Crear algunos comentarios de prueba para verificar que funciona
    console.log('Tabla no existe, creándola manualmente...');
    console.log('⚠️  Nota: Necesitas crear la tabla manualmente en Supabase Dashboard');
    console.log(`
    SQL para crear la tabla:
    
    CREATE TABLE ticket_comments (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      ticket_id UUID NOT NULL REFERENCES tickets(id) ON DELETE CASCADE,
      content TEXT NOT NULL,
      author VARCHAR(255) NOT NULL,
      is_internal BOOLEAN DEFAULT false,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX idx_ticket_comments_ticket_id ON ticket_comments(ticket_id);
    CREATE INDEX idx_ticket_comments_created_at ON ticket_comments(created_at);
    
    ALTER TABLE ticket_comments ENABLE ROW LEVEL SECURITY;
    
    CREATE POLICY "Enable all operations for authenticated users" ON ticket_comments
    FOR ALL USING (true);
    `);
    
  } catch (error) {
    console.error('Error:', error);
  }
}

createCommentsTable();
