
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { NotificationForm } from '@/components/NotificationForm';
import { useNotifications } from '@/hooks/useNotifications';

const AccessRequest = () => {
  const navigate = useNavigate();
  const { addNotification } = useNotifications();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100">
      {/* Header with back button */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-blue-200 py-4">
        <div className="container mx-auto px-6">
          <Button
            variant="ghost"
            onClick={() => navigate('/')}
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver al Inicio
          </Button>
        </div>
      </header>

      {/* Main content */}
      <main className="container mx-auto px-6 py-8">
        <div className="max-w-5xl mx-auto">
          <NotificationForm onAddNotification={addNotification} />
        </div>
      </main>
    </div>
  );
};

export default AccessRequest;
