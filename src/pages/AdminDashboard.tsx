import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { theme } from '@/lib/theme';
import { 
  LayoutDashboard, 
  Ticket, 
  Package, 
  Users, 
  UserPlus, 
  Bell, 
  BarChart3, 
  LogOut, 
  Settings,
  Shield,
  Crown,
  Eye,
  AlertTriangle,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle as X,
  RefreshCw,
  Activity,
  Zap
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { SuperiorTicketManager } from '@/components/SuperiorTicketManager';
import { AdvancedAssetManager } from '@/components/AdvancedAssetManager';
import { SimpleUserManagement } from '@/components/SimpleUserManagement';
import { EnhancedNotifications } from '@/components/EnhancedNotifications';
import { ReportsAnalytics } from '@/components/ReportsAnalytics';
import AccessRequestManager from '@/components/AccessRequestManager';

interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'superadmin' | 'admin' | 'viewer';
  active: boolean;
  last_login?: string;
}

interface DashboardStats {
  totalTickets: number;
  openTickets: number;
  closedTickets: number;
  totalUsers: number;
  activeUsers: number;
  totalAssets: number;
  pendingRequests: number;
}

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, signOut } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState<DashboardStats>({
    totalTickets: 0,
    openTickets: 0,
    closedTickets: 0,
    totalUsers: 0,
    activeUsers: 0,
    totalAssets: 0,
    pendingRequests: 0
  });
  const [loading, setLoading] = useState(true);
  const [newTicketAlerts, setNewTicketAlerts] = useState(0);
  const [lastTicketCount, setLastTicketCount] = useState(0);
  const [recentUpdates, setRecentUpdates] = useState<any[]>([]);

  // Fetch dashboard stats
  const fetchStats = async (skipNewTicketCheck = false) => {
    try {
      setLoading(true);
      
      // Fetch tickets stats
      const { data: ticketsData } = await supabase
        .from('tickets')
        .select('id, status, created_at, updated_at, title, priority')
        .order('updated_at', { ascending: false });
      
      // Fetch recent ticket updates (last 10 tickets with both created and updated)
      const recentTickets = ticketsData?.slice(0, 10).map(ticket => {
        const isRecentlyUpdated = ticket.updated_at && 
          new Date(ticket.updated_at).getTime() > new Date(ticket.created_at).getTime() + 60000; // 1 minute difference
        
        return {
          ...ticket,
          type: 'ticket',
          action: isRecentlyUpdated ? 'updated' : 'created',
          timestamp: isRecentlyUpdated ? ticket.updated_at : ticket.created_at,
          isUpdate: isRecentlyUpdated
        };
      }).slice(0, 5) || [];
      
      setRecentUpdates(recentTickets);
      
      // Fetch users stats
      const { data: usersData } = await supabase
        .from('admin_users')
        .select('active');
      
      // Fetch access requests stats
      const { data: accessData, error: accessError } = await supabase
        .from('notificaciones_acceso')
        .select('*')
        .eq('estado', 'pendiente');

      if (accessError) {
        console.error('❌ Error fetching access requests:', accessError);
      }
      
      const totalTickets = ticketsData?.length || 0;
      const openTickets = ticketsData?.filter(t => t.status !== 'closed').length || 0;
      const closedTickets = ticketsData?.filter(t => t.status === 'closed').length || 0;
      
      // Check for new tickets only if not skipping and we have a baseline
      if (!skipNewTicketCheck && lastTicketCount > 0 && totalTickets > lastTicketCount) {
        const newTicketsCount = totalTickets - lastTicketCount;
        console.log(`🆕 Detected ${newTicketsCount} new tickets. Current: ${totalTickets}, Previous: ${lastTicketCount}`);
        
        setNewTicketAlerts(prev => {
          const newCount = prev + newTicketsCount;
          console.log(`🔢 Updating alert count from ${prev} to ${newCount}`);
          return newCount;
        });
        
        // Show toast notification for new tickets
        const latestTicket = ticketsData?.[0];
        if (latestTicket) {
          toast({
            title: "🎫 Nuevo Ticket Recibido",
            description: `"${latestTicket.title || 'Sin título'}" - Prioridad: ${latestTicket.priority || 'Normal'}`,
            duration: 8000,
          });
          
          console.log('🔔 Toast notification sent for ticket:', latestTicket.title);
        }
      }
      
      // Always update the last ticket count
      setLastTicketCount(totalTickets);
      
      const totalUsers = usersData?.length || 0;
      const activeUsers = usersData?.filter(u => u.active).length || 0;
      
      const pendingRequests = accessData?.length || 0;

      setStats({
        totalTickets,
        openTickets,
        closedTickets,
        totalUsers,
        activeUsers,
        totalAssets: 0, // This would come from assets table when implemented
        pendingRequests
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats(true); // Skip new ticket check on initial load
    
    // Set up real-time subscription for new tickets
    console.log('🔄 Setting up real-time subscription for tickets...');
    
    const subscription = supabase
      .channel('admin-dashboard-tickets')
      .on('postgres_changes', { 
        event: 'INSERT', 
        schema: 'public', 
        table: 'tickets' 
      }, (payload) => {
        console.log('🎫 New ticket detected in dashboard:', payload);
        
        // Immediately increment alert counter
        setNewTicketAlerts(prev => {
          const newCount = prev + 1;
          console.log(`🚨 Alert count incremented from ${prev} to ${newCount}`);
          return newCount;
        });
        
        // Show immediate toast notification
        const newTicket = payload.new as any;
        if (newTicket) {
          toast({
            title: "🎫 Nuevo Ticket Recibido",
            description: `"${newTicket.title || 'Sin título'}" - Prioridad: ${newTicket.priority || 'Normal'}`,
            duration: 8000,
          });
          
          console.log('🔔 Toast notification sent for ticket:', newTicket.title);
        }
        
        // Refresh stats after a delay, but skip new ticket check to avoid conflicts
        setTimeout(() => {
          console.log('🔄 Refreshing stats after new ticket (skip check)...');
          fetchStats(true);
        }, 1000);
      })
      .on('postgres_changes', { 
        event: 'UPDATE', 
        schema: 'public', 
        table: 'tickets' 
      }, (payload) => {
        console.log('🔄 Ticket updated in dashboard:', payload);
        
        // Show toast notification for ticket updates
        const updatedTicket = payload.new as any;
        const oldTicket = payload.old as any;
        
        if (updatedTicket) {
          let updateMessage = '';
          
          // Check what was updated
          if (oldTicket.status !== updatedTicket.status) {
            updateMessage = `Estado cambiado a: ${updatedTicket.status}`;
          } else if (oldTicket.priority !== updatedTicket.priority) {
            updateMessage = `Prioridad cambiada a: ${updatedTicket.priority}`;
          } else {
            updateMessage = 'Ticket actualizado';
          }
          
          toast({
            title: "🔄 Ticket Actualizado",
            description: `"${updatedTicket.title || 'Sin título'}" - ${updateMessage}`,
            duration: 6000,
          });
          
          console.log('🔔 Update notification sent for ticket:', updatedTicket.title);
        }
        
        // Refresh stats to update the recent activity panel
        setTimeout(() => {
          console.log('🔄 Refreshing stats after ticket update...');
          fetchStats(true);
        }, 500);
      })
      .subscribe((status) => {
        console.log('📡 Subscription status:', status);
        if (status === 'SUBSCRIBED') {
          console.log('✅ Successfully subscribed to ticket changes');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Channel error in subscription');
        } else if (status === 'TIMED_OUT') {
          console.error('⏰ Subscription timed out');
        } else if (status === 'CLOSED') {
          console.log('🔌 Subscription closed');
        }
      });

    console.log('✅ Real-time subscription created:', subscription);

    return () => {
      console.log('🔌 Unsubscribing from real-time tickets...');
      subscription.unsubscribe();
    };
  }, []);

  const handleLogout = () => {
    signOut();
    toast({
      title: "Sesión Cerrada",
      description: "Has cerrado sesión exitosamente",
    });
    navigate('/');
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'superadmin': return Crown;
      case 'admin': return Shield;
      case 'viewer': return Eye;
      default: return Shield;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'superadmin': return 'bg-purple-100 text-purple-800';
      case 'admin': return 'bg-blue-100 text-blue-800';
      case 'viewer': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const canAccessTab = (tab: string) => {
    if (!user) return false;
    
    switch (tab) {
      case 'overview':
      case 'tickets':
        return true; // All roles can access
      case 'inventory':
      case 'notifications':
      case 'reports':
        return user.role === 'superadmin' || user.role === 'admin';
      case 'users':
        return user.role === 'superadmin' || user.role === 'admin';
      case 'access-requests':
        return user.role === 'superadmin';
      default:
        return false;
    }
  };

  const getAvailableTabs = () => {
    const tabs = [
      { id: 'overview', label: 'Resumen', icon: LayoutDashboard },
      { id: 'tickets', label: 'Tickets', icon: Ticket },
    ];

    if (user?.role === 'superadmin' || user?.role === 'admin') {
      tabs.push(
        { id: 'inventory', label: 'Inventario', icon: Package },
        { id: 'users', label: 'Usuarios', icon: Users },
        { id: 'notifications', label: 'Notificaciones', icon: Bell },
        { id: 'reports', label: 'Reportes', icon: BarChart3 }
      );
    }

    if (user?.role === 'superadmin') {
      tabs.push(
        { id: 'access-requests', label: 'Solicitudes', icon: UserPlus }
      );
    }

    return tabs;
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    
    // Clear ticket alerts when user clicks on tickets tab
    if (value === 'tickets' && newTicketAlerts > 0) {
      setNewTicketAlerts(0);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verificando autenticación...</p>
        </div>
      </div>
    );
  }

  const RoleIcon = getRoleIcon(user.role);
  const availableTabs = getAvailableTabs();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50 backdrop-blur-sm bg-white/95">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0">
                  <div className="p-2 bg-blue-600 rounded-lg">
                    <Shield className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Panel de Administración</h1>
                  <p className="text-sm text-gray-600">MSX International - Valencia HUB</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* User Info */}
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{user.name}</p>
                  <div className="flex items-center gap-2">
                    <Badge className={`text-xs ${getRoleColor(user.role)}`}>
                      <RoleIcon className="h-3 w-3 mr-1" />
                      {user.role === 'superadmin' ? 'Super Admin' : 
                       user.role === 'admin' ? 'Admin' : 'Viewer'}
                    </Badge>
                  </div>
                </div>
                
                <Button onClick={handleLogout} variant="outline" size="sm">
                  <LogOut className="h-4 w-4 mr-2" />
                  Cerrar Sesión
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="grid w-full grid-cols-7 lg:grid-cols-7">
            {availableTabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className={`flex items-center gap-2 px-3 py-2 text-sm font-medium transition-colors ${
                    !canAccessTab(tab.id) 
                      ? 'opacity-50 cursor-not-allowed' 
                      : 'hover:bg-gray-100'
                  }`}
                  disabled={!canAccessTab(tab.id)}
                >
                  <Icon className="h-4 w-4" />
                  {tab.id === 'tickets' && newTicketAlerts > 0 && (
                    <Badge className="ml-1 text-xs bg-red-500 text-white px-1 py-0">
                      {newTicketAlerts}
                    </Badge>
                  )}
                  <span className="hidden sm:inline">{tab.label}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* New Ticket Alert Banner */}
            {newTicketAlerts > 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -20 }}
                transition={{ duration: 0.3 }}
                className="relative mb-6"
              >
                <div className="bg-gradient-to-r from-red-500 to-red-600 rounded-lg shadow-lg border-2 border-red-400 relative overflow-hidden">
                  <div className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                            <AlertTriangle className="h-6 w-6 text-red-500" />
                          </div>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-bold text-white">
                            🚨 {newTicketAlerts === 1 ? 'Nuevo Ticket Recibido' : `${newTicketAlerts} Nuevos Tickets Recibidos`}
                          </h3>
                          <p className="text-red-100 text-sm">
                            {newTicketAlerts === 1 
                              ? 'Se ha recibido un nuevo ticket que requiere atención inmediata.'
                              : `Se han recibido ${newTicketAlerts} nuevos tickets que requieren atención inmediata.`
                            }
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Button
                          onClick={() => {
                            console.log('🎫 Navigating to tickets tab...');
                            handleTabChange('tickets');
                          }}
                          className="bg-white text-red-600 hover:bg-red-50 font-semibold px-6 py-2 shadow-md transition-colors duration-200"
                          type="button"
                        >
                          <Ticket className="h-4 w-4 mr-2" />
                          Ver Tickets
                        </Button>
                        <Button
                          onClick={() => {
                            console.log('❌ Dismissing ticket alerts...');
                            setNewTicketAlerts(0);
                          }}
                          variant="ghost"
                          size="sm"
                          className="text-white hover:bg-red-600 p-2 transition-colors duration-200"
                          type="button"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">Resumen del Sistema</h2>
              <Button onClick={() => fetchStats()} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Actualizar
              </Button>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card className="hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-blue-700">Total Tickets</p>
                        <p className="text-3xl font-bold text-blue-900">{stats.totalTickets}</p>
                      </div>
                      <div className="p-3 bg-blue-500 rounded-lg shadow-lg">
                        <Ticket className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center gap-2">
                      <div className="flex items-center gap-1 text-sm">
                        <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                        <span className="text-blue-700 font-medium">{stats.openTickets} abiertos</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-blue-700 font-medium">{stats.closedTickets} cerrados</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-700">Usuarios Admin</p>
                        <p className="text-3xl font-bold text-green-900">{stats.totalUsers}</p>
                      </div>
                      <div className="p-3 bg-green-500 rounded-lg shadow-lg">
                        <Users className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-green-700 font-medium">{stats.activeUsers} activos</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-purple-700">Inventario</p>
                        <p className="text-3xl font-bold text-purple-900">{stats.totalAssets}</p>
                      </div>
                      <div className="p-3 bg-purple-500 rounded-lg shadow-lg">
                        <Package className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center gap-2">
                      <Activity className="h-4 w-4 text-purple-600" />
                      <span className="text-sm text-purple-700 font-medium">Activos gestionados</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card className="hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-orange-700">Solicitudes</p>
                        <p className="text-3xl font-bold text-orange-900">{stats.pendingRequests}</p>
                      </div>
                      <div className="p-3 bg-orange-500 rounded-lg shadow-lg">
                        <UserPlus className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center gap-2">
                      <Clock className="h-4 w-4 text-orange-600" />
                      <span className="text-sm text-orange-700 font-medium">Pendientes</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Recent Updates Dashboard */}
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-indigo-100">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold text-gray-800 flex items-center gap-2">
                  <Activity className="h-5 w-5 text-blue-500" />
                  Actualizaciones Recientes
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
                    <span className="ml-2 text-gray-600">Cargando actualizaciones...</span>
                  </div>
                ) : recentUpdates.length > 0 ? (
                  <div className="space-y-3">
                    {recentUpdates.map((update, index) => (
                      <motion.div
                        key={`${update.id || index}-${update.timestamp}`}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${
                            update.action === 'updated' 
                              ? 'bg-orange-100' 
                              : 'bg-blue-100'
                          }`}>
                            {update.action === 'updated' ? (
                              <RefreshCw className="h-4 w-4 text-orange-600" />
                            ) : (
                              <Ticket className="h-4 w-4 text-blue-600" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 truncate max-w-xs">
                              {update.title || 'Ticket sin título'}
                            </p>
                            <p className="text-sm text-gray-500">
                              {update.action === 'updated' ? (
                                <span className="flex items-center gap-1">
                                  <RefreshCw className="h-3 w-3" />
                                  Actualizado recientemente
                                </span>
                              ) : (
                                <span className="flex items-center gap-1">
                                  <Ticket className="h-3 w-3" />
                                  Nuevo ticket creado
                                </span>
                              )}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant={
                              update.priority === 'critical' ? 'destructive' :
                              update.priority === 'high' ? 'default' :
                              'secondary'
                            }
                            className="text-xs"
                          >
                            {update.priority || 'Normal'}
                          </Badge>
                          <Badge 
                            variant={
                              update.status === 'closed' ? 'default' :
                              update.status === 'en_progreso' ? 'secondary' :
                              'outline'
                            }
                            className="text-xs"
                          >
                            {update.status === 'pendiente' ? 'Pendiente' :
                             update.status === 'en_progreso' ? 'En Progreso' :
                             update.status === 'closed' ? 'Cerrado' :
                             update.status || 'Sin Estado'}
                          </Badge>
                          <span className="text-xs text-gray-400">
                            {new Date(update.timestamp).toLocaleDateString('es-ES', {
                              day: '2-digit',
                              month: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Activity className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                    <p>No hay actualizaciones recientes</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="border-0 shadow-lg bg-gradient-to-br from-gray-50 to-white">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold text-gray-800 flex items-center gap-2">
                  <Zap className="h-5 w-5 text-blue-500" />
                  Acciones Rápidas
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button 
                    onClick={() => setActiveTab('tickets')} 
                    className="h-auto p-6 flex flex-col items-center gap-3 bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border border-blue-200 text-blue-700 hover:text-blue-800 shadow-md hover:shadow-lg transition-all duration-300"
                  >
                    <Ticket className="h-8 w-8" />
                    <span className="font-semibold">Gestionar Tickets</span>
                    {newTicketAlerts > 0 && (
                      <Badge className="ml-1 text-xs bg-red-500 text-white px-2 py-1 animate-pulse">
                        {newTicketAlerts}
                      </Badge>
                    )}
                  </Button>
                  
                  {canAccessTab('users') && (
                    <Button 
                      onClick={() => setActiveTab('users')} 
                      className="h-auto p-6 flex flex-col items-center gap-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 text-white"
                    >
                      <Users className="h-8 w-8" />
                      <span className="font-semibold">Administrar Usuarios</span>
                    </Button>
                  )}
                  
                  {canAccessTab('reports') && (
                    <Button 
                      onClick={() => setActiveTab('reports')} 
                      className="h-auto p-6 flex flex-col items-center gap-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 text-white"
                    >
                      <BarChart3 className="h-8 w-8" />
                      <span className="font-semibold">Ver Reportes</span>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Other Tabs */}
          <TabsContent value="tickets" className="space-y-4">
            <SuperiorTicketManager projectId="ffxtpwgrkvicknkjvzgo" />
          </TabsContent>

          {canAccessTab('inventory') && (
            <TabsContent value="inventory" className="space-y-4">
              <AdvancedAssetManager projectId="ffxtpwgrkvicknkjvzgo" />
            </TabsContent>
          )}

          {canAccessTab('users') && (
            <TabsContent value="users">
              <SimpleUserManagement />
            </TabsContent>
          )}

          {canAccessTab('notifications') && (
            <TabsContent value="notifications">
              <EnhancedNotifications />
            </TabsContent>
          )}

          {canAccessTab('reports') && (
            <TabsContent value="reports">
              <ReportsAnalytics />
            </TabsContent>
          )}

          {canAccessTab('access-requests') && (
            <TabsContent value="access-requests">
              <AccessRequestManager />
            </TabsContent>
          )}
        </Tabs>
      </main>
    </div>
  );
};

export default AdminDashboard;
