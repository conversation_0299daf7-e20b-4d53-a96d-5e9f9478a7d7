import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { 
  Shield, 
  Users, 
  MapPin,
  Building2,
  ArrowRight,
  Headphones,
  Lock
} from 'lucide-react';

const Home = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 shadow-sm">
        <div className="container mx-auto px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              {/* Logo de Karedesk discreto con tooltip */}
              <div className="group relative flex items-center gap-2">
                <div className="flex items-center gap-1.5">
                  <img 
                    src="/lovable-uploads/1cfd5d87-5536-4d30-aa9d-d1392bff3017.png" 
                    alt="Karedesk" 
                    className="h-5 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity"
                  />
                  <span className="text-xs font-medium text-blue-600">by Karedesk</span>
                </div>
                <div className="absolute -top-8 left-0 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  Soluciones IT para MSX International
                </div>
              </div>
              
              {/* MSX International prominente */}
              <div className="flex items-center gap-3">
                <img 
                  src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png" 
                  alt="MSX International" 
                  className="h-8 w-auto object-contain"
                />
                <div>
                  <div className="text-lg font-bold text-gray-900">MSX International</div>
                  <div className="flex items-center gap-2 text-sm text-blue-600">
                    <MapPin className="h-3 w-3" />
                    <span className="font-medium">Valencia HUB</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                <Building2 className="h-3 w-3 mr-1" />
                Portal IT
              </Badge>
              <Button
                onClick={() => navigate('/admin-login')}
                className="bg-blue-600 hover:bg-blue-700 text-white shadow-md"
                size="sm"
              >
                <Shield className="h-4 w-4 mr-2" />
                Administración
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-8 py-12">
        {/* Hero Section */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Portal de Servicios IT
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Gestión de accesos y soporte técnico para MSX International Valencia HUB
          </p>
        </motion.div>

        {/* Services Grid */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-16"
        >
          {/* Soporte Técnico */}
          <Card className="group hover:shadow-xl transition-all duration-300 border-gray-200 bg-white">
            <CardContent className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow">
                  <Headphones className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">Soporte Técnico</h3>
                  <p className="text-green-600 font-medium">Reportar Incidencias</p>
                </div>
              </div>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Crea tickets para reportar problemas técnicos, solicitar asistencia especializada o realizar consultas al equipo de IT de MSX International.
              </p>
              <Button 
                onClick={() => navigate('/tickets')}
                className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg group-hover:shadow-xl transition-all"
              >
                <Headphones className="mr-2 h-5 w-5" />
                <span>Crear Ticket de Soporte</span>
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
            </CardContent>
          </Card>

          {/* Control de Accesos */}
          <Card className="group hover:shadow-xl transition-all duration-300 border-gray-200 bg-white">
            <CardContent className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow">
                  <Lock className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">Control de Accesos</h3>
                  <p className="text-blue-600 font-medium">Solicitar Permisos</p>
                </div>
              </div>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Solicita acceso a sistemas, aplicaciones y recursos corporativos de MSX International de manera segura y controlada.
              </p>
              <Button 
                onClick={() => navigate('/access-request')}
                className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg group-hover:shadow-xl transition-all"
              >
                <Lock className="mr-2 h-5 w-5" />
                <span>Solicitar Acceso</span>
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="container mx-auto px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <img 
                src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png" 
                alt="MSX International" 
                className="h-6 w-auto object-contain brightness-0 invert"
              />
              <div>
                <div className="font-semibold">MSX International</div>
                <div className="text-sm text-gray-400">Valencia HUB, España</div>
              </div>
            </div>
            
            <div className="flex flex-col items-end text-right">
              <div className="text-xs text-gray-400 mb-1">Plataforma desarrollada por</div>
              <div className="flex items-center gap-1.5">
                <img 
                  src="/lovable-uploads/1cfd5d87-5536-4d30-aa9d-d1392bff3017.png" 
                  alt="Karedesk" 
                  className="h-4 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity"
                />
                <span className="text-sm font-medium text-gray-300">Karedesk</span>
                <span className="text-xs text-gray-500">IT Solutions</span>
              </div>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-800 text-center text-sm text-gray-400">
            2025 MSX International Valencia HUB. Portal de servicios IT interno.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;
