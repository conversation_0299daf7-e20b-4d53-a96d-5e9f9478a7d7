
import { useState, useEffect, useCallback, useMemo, Dispatch, SetStateAction } from 'react';
import { useQuery, useMutation, useQueryClient, UseMutateFunction, QueryObserverResult, RefetchOptions } from '@tanstack/react-query';
import { supabase, handleSupabaseError } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import type { PostgrestFilterBuilder } from '@supabase/postgrest-js';
import type { Database } from '@/integrations/supabase/types';

export interface Notification {
  id: string;
  empleado_nombre: string;
  empleado_cdsid: string;
  region: string;
  plataformas_faltantes: string[];
  descripcion: string;
  prioridad: 'baja' | 'media' | 'alta' | 'critica';
  estado: 'pendiente' | 'en_proceso' | 'completado' | 'rechazado';
  created_at: string;
  updated_at: string;
  assigned_to?: string;
  resolved_at?: string;
  tags?: string[];
}

export interface NotificationFilters {
  estado?: string;
  prioridad?: string;
  region?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  assignedTo?: string;
}

export interface NotificationStats {
  total: number;
  pendientes: number;
  enProceso: number;
  completados: number;
  porPrioridad: Record<string, number>;
  porRegion: Record<string, number>;
}

// Simplified input type to avoid circular references
interface NotificationInput {
  empleado_nombre: string;
  empleado_cdsid: string;
  region: string;
  plataformas_faltantes: string[];
  descripcion: string;
  prioridad: 'baja' | 'media' | 'alta' | 'critica';
  assigned_to?: string;
  tags?: string[];
}

interface UseNotificationsReturn {
  notifications: Notification[];
  stats: NotificationStats;
  isLoading: boolean;
  error: string | null;
  addNotification: UseMutateFunction<Notification | null, Error, NotificationInput, unknown>;
  updateNotification: UseMutateFunction<Notification | null, Error, { id: string; updates: Partial<Notification>; }, unknown>;
  deleteNotification: UseMutateFunction<void, Error, string, unknown>;
  refetch: (options?: RefetchOptions) => Promise<QueryObserverResult<Notification[], Error>>;
  isAdding: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  realTimeEnabled: boolean;
  setRealTimeEnabled: Dispatch<SetStateAction<boolean>>;
}

type NotificationsTable = Database['public']['Tables']['notificaciones_acceso'];
type NotificationsRow = NotificationsTable['Row'];

export const useNotifications = (filters?: NotificationFilters): UseNotificationsReturn => {
  const queryClient = useQueryClient();
  const [realTimeEnabled, setRealTimeEnabled] = useState(true);

  // Build query with filters - using explicit typing to prevent deep instantiation
  const buildQuery = useCallback((): PostgrestFilterBuilder<Database['public'], NotificationsRow, NotificationsRow[]> => {
    let query = supabase
      .from('notificaciones_acceso')
      .select('*')
      .order('created_at', { ascending: false });

    if (filters?.estado && filters.estado !== 'all') {
      query = query.eq('estado', filters.estado);
    }
    
    if (filters?.prioridad && filters.prioridad !== 'all') {
      query = query.eq('prioridad', filters.prioridad);
    }
    
    if (filters?.region && filters.region !== 'all') {
      query = query.eq('region', filters.region);
    }
    
    if (filters?.assignedTo) {
      query = query.eq('assigned_to', filters.assignedTo);
    }
    
    if (filters?.search) {
      query = query.or(`empleado_nombre.ilike.%${filters.search}%,empleado_cdsid.ilike.%${filters.search}%,descripcion.ilike.%${filters.search}%`);
    }
    
    if (filters?.dateFrom) {
      query = query.gte('created_at', filters.dateFrom);
    }
    
    if (filters?.dateTo) {
      query = query.lte('created_at', filters.dateTo);
    }

    return query;
  }, [filters]);

  // Fetch notifications with explicit typing
  const {
    data: notifications = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['notifications', JSON.stringify(filters)],
    queryFn: async (): Promise<Notification[]> => {
      const { data, error } = await buildQuery();
      if (error) {
        throw new Error(handleSupabaseError(error, 'Fetching notifications'));
      }
      return (data || []) as Notification[];
    },
    staleTime: 1000 * 60 * 2, // 2 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 3,
  });

  // Calculate statistics
  const stats: NotificationStats = useMemo(() => {
    const total = notifications.length;
    const pendientes = notifications.filter(n => n.estado === 'pendiente').length;
    const enProceso = notifications.filter(n => n.estado === 'en_proceso').length;
    const completados = notifications.filter(n => n.estado === 'completado').length;
    
    const porPrioridad = notifications.reduce((acc, n) => {
      acc[n.prioridad] = (acc[n.prioridad] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const porRegion = notifications.reduce((acc, n) => {
      acc[n.region] = (acc[n.region] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      pendientes,
      enProceso,
      completados,
      porPrioridad,
      porRegion
    };
  }, [notifications]);

  // Add notification mutation
  const addNotificationMutation = useMutation({
    mutationFn: async (newNotification: NotificationInput): Promise<Notification | null> => {
      const { data, error } = await supabase
        .from('notificaciones_acceso')
        .insert([{
          ...newNotification,
          estado: 'pendiente' as const,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        throw new Error(handleSupabaseError(error, 'Adding notification'));
      }
      
      return data as Notification | null;
    },
    onSuccess: (data) => {
      if (!data) return;
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: "Solicitud Creada",
        description: `Se ha creado la solicitud para ${data.empleado_nombre}`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Update notification mutation
  const updateNotificationMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string, updates: Partial<Notification> }): Promise<Notification | null> => {
      const { data, error } = await supabase
        .from('notificaciones_acceso')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new Error(handleSupabaseError(error, 'Updating notification'));
      }
      
      return data as Notification | null;
    },
    onSuccess: (data) => {
      if (!data) return;
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: "Solicitud Actualizada",
        description: `Se ha actualizado la solicitud de ${data.empleado_nombre}`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Delete notification mutation
  const deleteNotificationMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('notificaciones_acceso')
        .delete()
        .eq('id', id);

      if (error) {
        throw new Error(handleSupabaseError(error, 'Deleting notification'));
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: "Solicitud Eliminada",
        description: "La solicitud ha sido eliminada correctamente",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Real-time subscription
  useEffect(() => {
    if (!realTimeEnabled) return;

    const channel = supabase
      .channel('notifications_channel')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notificaciones_acceso'
        },
        (payload) => {
          console.log('Real-time update:', payload);
          queryClient.invalidateQueries({ queryKey: ['notifications'] });
          
          // Show notification for new items
          if (payload.eventType === 'INSERT') {
            toast({
              title: "Nueva Solicitud",
              description: `Nueva solicitud de acceso recibida`,
            });
          }
        }
      )
      .subscribe();

    return () => {
      channel.unsubscribe();
    };
  }, [queryClient, realTimeEnabled]);

  return {
    // Data
    notifications,
    stats,
    isLoading,
    error: error ? handleSupabaseError(error, 'Notifications hook') : null,
    
    // Actions
    addNotification: addNotificationMutation.mutate,
    updateNotification: updateNotificationMutation.mutate,
    deleteNotification: deleteNotificationMutation.mutate,
    refetch,
    
    // Loading states
    isAdding: addNotificationMutation.isPending,
    isUpdating: updateNotificationMutation.isPending,
    isDeleting: deleteNotificationMutation.isPending,
    
    // Settings
    realTimeEnabled,
    setRealTimeEnabled,
  };
};
