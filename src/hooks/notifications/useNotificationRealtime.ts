
import { useEffect, useState, Dispatch, SetStateAction } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

export const useNotificationRealtime = () => {
  const queryClient = useQueryClient();
  const [realTimeEnabled, setRealTimeEnabled] = useState(true);

  // Real-time subscription
  useEffect(() => {
    if (!realTimeEnabled) return;

    console.log('Setting up real-time subscription');
    const channel = supabase
      .channel('notifications_channel')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'solicitudes_acceso'
        },
        (payload) => {
          console.log('Real-time update:', payload);
          queryClient.invalidateQueries({ queryKey: ['notifications'] });
          
          // Show notification for new items
          if (payload.eventType === 'INSERT') {
            toast({
              title: "Nueva Solicitud",
              description: `Nueva solicitud de acceso recibida`,
            });
          }
        }
      )
      .subscribe();

    return () => {
      console.log('Cleaning up real-time subscription');
      channel.unsubscribe();
    };
  }, [queryClient, realTimeEnabled]);

  return {
    realTimeEnabled,
    setRealTimeEnabled,
  };
};
