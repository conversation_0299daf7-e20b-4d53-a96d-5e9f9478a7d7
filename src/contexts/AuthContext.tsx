import React, { createContext, useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import bcrypt from 'bcryptjs';

export type AdminUser = {
  id: string;
  email: string;
  name: string | null;
  role: 'superadmin' | 'admin' | 'viewer';
  active: boolean;
};

type AuthContextType = {
  user: AdminUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
  hasRole: (roles: string[]) => boolean;
  setSession: (userData: AdminUser, persist?: boolean) => void;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);

  const setSession = (userData: AdminUser, persist: boolean = true) => {
    console.log('📝 Setting user session:', userData.email);
    setUser(userData);
    if (persist) {
      localStorage.setItem('admin_user', JSON.stringify(userData));
    }
  };

  const clearSession = () => {
    console.log('🗑️ Clearing user session');
    setUser(null);
    localStorage.removeItem('admin_user');
  };

  useEffect(() => {
    const checkSession = async () => {
      console.log('🔍 Checking session...');
      
      try {
        // Primero verificar si hay una sesión activa en Supabase
        const { data: { session }, error } = await supabase.auth.getSession();
        console.log('📋 Supabase session:', { session: !!session, error });

        if (session?.user) {
          console.log('🔍 Found Supabase session, fetching profile...');
          
          // Obtener perfil del usuario
          const { data: profile, error: profileError } = await supabase
            .from('admin_users')
            .select('*')
            .eq('id', session.user.id)
            .single();
            
          console.log('👤 Profile result:', { profile, profileError });

          if (profile) {
            const userData: AdminUser = {
              id: session.user.id,
              email: session.user.email || '',
              name: profile.name,
              role: profile.role as 'superadmin' | 'admin' | 'viewer',
              active: profile.active
            };
            setSession(userData);
          } else {
            console.log('❌ No profile found, checking localStorage...');
            // Si no hay perfil en Supabase, verificar localStorage
            const storedUser = localStorage.getItem('admin_user');
            if (storedUser) {
              console.log('💾 Found user in localStorage');
              setUser(JSON.parse(storedUser));
            }
          }
        } else {
          console.log('🔍 No Supabase session, checking localStorage...');
          // Si no hay sesión en Supabase, verificar localStorage
          const storedUser = localStorage.getItem('admin_user');
          if (storedUser) {
            console.log('💾 Found user in localStorage');
            setUser(JSON.parse(storedUser));
          }
        }
      } catch (error) {
        console.error('❌ Error checking session:', error);
        clearSession();
      } finally {
        setLoading(false);
      }
    };

    checkSession();

    // Escuchar cambios en la autenticación
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔐 Auth state change:', event, session?.user?.email);
      
      if (event === 'SIGNED_IN' && session?.user) {
        console.log('🔐 Auth state change: SIGNED_IN', session.user.email);
        // NO hacer nada aquí - dejar que signIn maneje todo
        console.log('⏸️ Skipping onAuthStateChange processing - letting signIn handle it');
      } else if (event === 'SIGNED_OUT') {
        console.log('🚪 User signed out');
        clearSession();
      }
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  const navigate = useNavigate();

  // Direct authentication with admin_users table
  const signInWithAdminUsers = async (email: string, password: string) => {
    console.log('🔐 Attempting direct admin authentication for:', email);

    try {
      // Get user from admin_users table
      const { data: adminUser, error: fetchError } = await supabase
        .from('admin_users')
        .select('*')
        .eq('email', email.toLowerCase())
        .eq('active', true)
        .single();

      if (fetchError || !adminUser) {
        console.error('❌ Admin user not found:', fetchError);
        throw new Error('Usuario no encontrado o inactivo');
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, adminUser.password_hash);

      if (!isValidPassword) {
        console.error('❌ Invalid password for admin user');
        throw new Error('Credenciales inválidas');
      }

      console.log('✅ Direct admin authentication successful');

      // Create user session
      const userData: AdminUser = {
        id: adminUser.id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role as 'superadmin' | 'admin' | 'viewer',
        active: adminUser.active
      };

      setUser(userData);
      setSession(userData, true);

      console.log('🚀 Direct admin login successful, navigating to dashboard');
      navigate('/admin-dashboard', { replace: true });

    } catch (error) {
      console.error('❌ Direct admin authentication failed:', error);
      throw error;
    }
  };

  const signIn = async (email: string, password: string) => {
    console.log('🔑 Attempting login for:', email);
    try {
      // First try Supabase Auth
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      console.log('🔐 Login response:', { data: data?.user?.email, error });

      // If Supabase Auth fails, try direct admin_users authentication
      if (error) {
        console.log('🔄 Supabase Auth failed, trying direct admin authentication...');
        return await signInWithAdminUsers(email, password);
      }
      
      if (data?.user) {
        console.log('✅ Login successful, fetching profile...');
        
        // Obtener perfil inmediatamente después del login
        const { data: profile, error: profileError } = await supabase
          .from('admin_users')
          .select('*')
          .eq('id', data.user.id)
          .single();
          
        console.log('👤 Profile fetch result:', { profile, profileError });
        
        if (profile) {
          console.log('✅ Profile found, setting user and navigating');
          const adminUser: AdminUser = {
            id: profile.id,
            email: profile.email,
            name: profile.name,
            role: profile.role as 'superadmin' | 'admin' | 'viewer',
            active: profile.active
          };
          setUser(adminUser);
          setSession(adminUser, true);
          
          // Verificar que el estado se estableció correctamente
          console.log('🔍 User state after setting:', adminUser);
          console.log('🔍 Current user in context:', user);
          
          // Pequeño delay para asegurar que el estado se establezca
          setTimeout(() => {
            console.log('🚀 Navigating to dashboard after state update');
            console.log('🔍 Final user state before navigation:', user);
            navigate('/admin-dashboard', { replace: true });
          }, 100);
        } else {
          console.error('❌ No profile found for user:', data.user.id, profileError);
          throw new Error('No se encontró el perfil de usuario');
        }
      }
      
    } catch (error) {
      console.error('Error al iniciar sesión:', error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      clearSession();
      navigate('/admin-login');
    } catch (error) {
      console.error('Error al cerrar sesión:', error);
      throw error;
    }
  };

  const isAuthenticated = !!user?.id && user.active;

  const hasRole = (roles: string[]) => {
    if (!user) return false;
    return roles.includes(user.role);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        signIn,
        signOut,
        isAuthenticated,
        hasRole,
        setSession,
      }}
    >
      {!loading && children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth debe usarse dentro de un AuthProvider');
  }
  return context;
};
