
/* Professional Design Utilities */
@layer utilities {
  .gradient-msx {
    background: linear-gradient(135deg, hsl(var(--msx-red)) 0%, hsl(var(--msx-red-dark)) 100%);
  }
  
  .gradient-professional {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)/0.8) 100%);
  }
  
  .glass-effect {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.12);
  }
  
  .glass-effect-dark {
    background: rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }
  
  .animate-slide-up {
    animation: slideUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  .animate-fade-in-delay {
    animation: fadeInDelay 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .shadow-professional {
    box-shadow: 
      0 1px 3px 0 rgba(0, 0, 0, 0.1),
      0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .shadow-professional-lg {
    box-shadow: 
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.15);
  }

  .shadow-3xl-dark {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.4);
  }

  .animate-gradient {
    background-size: 400% 400%;
    animation: gradientShift 12s ease infinite;
  }

  .animate-float {
    animation: float 8s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 3s ease-in-out infinite alternate;
  }

  .animate-glow-msx {
    animation: glowMsx 3s ease-in-out infinite alternate;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-foreground via-primary to-foreground bg-clip-text text-transparent;
  }

  .border-gradient {
    border-image: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary)/0.3)) 1;
  }

  /* Status colors */
  .text-success {
    color: hsl(var(--success));
  }
  
  .text-warning {
    color: hsl(var(--warning));
  }
  
  .text-error {
    color: hsl(var(--error));
  }
  
  .text-info {
    color: hsl(var(--info));
  }

  .bg-success {
    background-color: hsl(var(--success));
  }
  
  .bg-warning {
    background-color: hsl(var(--warning));
  }
  
  .bg-error {
    background-color: hsl(var(--error));
  }
  
  .bg-info {
    background-color: hsl(var(--info));
  }
}
