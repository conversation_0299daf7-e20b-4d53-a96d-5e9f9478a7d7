import { motion, AnimatePresence } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { 
  User, 
  Clock, 
  AlertTriangle, 
  CheckCircle2, 
  XCircle, 
  MoreVertical,
  Clock4,
  AlertCircle,
  CheckCircle,
  X,
  Check,
  Loader2
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';
import { useState } from 'react';

const priorityIcons = {
  alta: <AlertTriangle className="w-4 h-4 text-red-500" />,
  media: <AlertCircle className="w-4 h-4 text-yellow-500" />,
  baja: <Clock4 className="w-4 h-4 text-blue-500" />,
};

const statusBadges = {
  pendiente: {
    text: 'Pendiente',
    variant: 'outline',
    icon: <Clock className="w-3 h-3 mr-1" />
  },
  en_proceso: {
    text: 'En Proceso',
    variant: 'secondary',
    icon: <Loader2 className="w-3 h-3 mr-1 animate-spin" />
  },
  completado: {
    text: 'Completado',
    variant: 'default',
    icon: <Check className="w-3 h-3 mr-1" />
  },
  rechazado: {
    text: 'Rechazado',
    variant: 'destructive',
    icon: <X className="w-3 h-3 mr-1" />
  }
};

export const AccessRequestCard = ({ 
  request, 
  onStatusChange,
  onDelete,
  isNew = false 
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleStatusChange = async (newStatus) => {
    setIsProcessing(true);
    try {
      await onStatusChange(request.id, newStatus);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDelete = async () => {
    if (confirm('¿Estás seguro de que deseas eliminar esta solicitud?')) {
      await onDelete(request.id);
    }
  };

  return (
    <motion.div
      initial={{ opacity: isNew ? 0 : 1, y: isNew ? 20 : 0 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, height: 0, marginBottom: 0 }}
      transition={{ duration: 0.3 }}
      className="relative"
    >
      <Card 
        className={`overflow-hidden transition-all duration-200 ${isNew ? 'ring-2 ring-blue-500' : ''} hover:shadow-lg`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className={`absolute top-0 left-0 w-1 h-full bg-${{
          alta: 'red-500',
          media: 'yellow-500',
          baja: 'blue-500'
        }[request.prioridad] || 'gray-500'}`} />
        
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <CardTitle className="text-lg flex items-center gap-2">
              <User className="w-5 h-5 text-muted-foreground" />
              {request.empleado_nombre}
              <span className="text-sm text-muted-foreground">
                ({request.empleado_cdsid})
              </span>
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant={statusBadges[request.estado]?.variant || 'outline'} className="gap-1">
                {statusBadges[request.estado]?.icon}
                {statusBadges[request.estado]?.text || request.estado}
              </Badge>
            </div>
          </div>
          <div className="flex items-center text-sm text-muted-foreground mt-1">
            <Clock className="w-3 h-3 mr-1" />
            {formatDistanceToNow(new Date(request.created_at), { 
              addSuffix: true,
              locale: es 
            })}
          </div>
        </CardHeader>

        <CardContent className="pb-2">
          <div className="mb-3">
            <p className="text-sm text-muted-foreground mb-1">Plataformas requeridas:</p>
            <div className="flex flex-wrap gap-1">
              {request.plataformas_faltantes?.map((plataforma, i) => (
                <Badge key={i} variant="outline" className="text-xs">
                  {plataforma}
                </Badge>
              )) || 'No especificadas'}
            </div>
          </div>
          
          {request.descripcion && (
            <div className="mb-3">
              <p className="text-sm text-muted-foreground mb-1">Descripción:</p>
              <p className="text-sm">{request.descripcion}</p>
            </div>
          )}
        </CardContent>

        <CardFooter className="pt-0 pb-4 px-4 flex flex-wrap gap-2">
          <motion.div 
            className="flex gap-2 w-full"
            animate={isHovered || isProcessing ? 'open' : 'closed'}
            variants={{
              open: { opacity: 1, height: 'auto' },
              closed: { opacity: 0, height: 0, overflow: 'hidden' }
            }}
            transition={{ duration: 0.2 }}
          >
            {request.estado !== 'en_proceso' && (
              <Button 
                variant="outline" 
                size="sm" 
                className="flex-1"
                onClick={() => handleStatusChange('en_proceso')}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle2 className="w-4 h-4 mr-2" />
                )}
                Tomar
              </Button>
            )}
            
            {request.estado === 'en_proceso' && (
              <>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => handleStatusChange('completado')}
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Check className="w-4 h-4 mr-2" />
                  )}
                  Completar
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => handleStatusChange('rechazado')}
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <X className="w-4 h-4 mr-2" />
                  )}
                  Rechazar
                </Button>
              </>
            )}
            
            <Button 
              variant="ghost" 
              size="icon" 
              className="ml-auto"
              onClick={handleDelete}
              disabled={isProcessing}
            >
              <Trash2 className="w-4 h-4 text-destructive" />
            </Button>
          </motion.div>
        </CardFooter>
      </Card>
    </motion.div>
  );
};
