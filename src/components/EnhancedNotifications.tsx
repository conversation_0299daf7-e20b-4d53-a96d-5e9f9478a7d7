import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  Bell, 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  XCircle,
  Clock,
  Send,
  Eye,
  EyeOff,
  Trash2,
  Plus,
  Filter,
  Search,
  Users,
  Megaphone,
  Settings
} from 'lucide-react';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success' | 'urgent';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'active' | 'dismissed' | 'expired';
  target_audience: 'all' | 'admins' | 'technicians' | 'managers';
  created_by: string;
  created_at: string;
  expires_at?: string;
  read_by: string[];
  actions?: {
    label: string;
    action: string;
    url?: string;
  }[];
}

export const EnhancedNotifications: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const { toast } = useToast();

  const [newNotification, setNewNotification] = useState({
    title: '',
    message: '',
    type: 'info' as const,
    priority: 'medium' as const,
    target_audience: 'all' as const,
    expires_at: ''
  });

  const notificationTypes = [
    { value: 'info', label: 'Información', icon: Info, color: 'bg-blue-100 text-blue-800' },
    { value: 'warning', label: 'Advertencia', icon: AlertTriangle, color: 'bg-yellow-100 text-yellow-800' },
    { value: 'error', label: 'Error', icon: XCircle, color: 'bg-red-100 text-red-800' },
    { value: 'success', label: 'Éxito', icon: CheckCircle, color: 'bg-green-100 text-green-800' },
    { value: 'urgent', label: 'Urgente', icon: Bell, color: 'bg-purple-100 text-purple-800' }
  ];

  const priorityLevels = [
    { value: 'low', label: 'Baja', color: 'bg-gray-100 text-gray-800' },
    { value: 'medium', label: 'Media', color: 'bg-blue-100 text-blue-800' },
    { value: 'high', label: 'Alta', color: 'bg-orange-100 text-orange-800' },
    { value: 'critical', label: 'Crítica', color: 'bg-red-100 text-red-800' }
  ];

  const audiences = [
    { value: 'all', label: 'Todos los usuarios' },
    { value: 'admins', label: 'Administradores' },
    { value: 'technicians', label: 'Técnicos' },
    { value: 'managers', label: 'Managers' }
  ];

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      
      // Mock notifications since we don't have a real notifications table
      const mockNotifications: Notification[] = [
        {
          id: '1',
          title: 'Mantenimiento programado del sistema',
          message: 'El sistema estará en mantenimiento el próximo domingo de 2:00 AM a 6:00 AM. Durante este tiempo, algunas funcionalidades pueden no estar disponibles.',
          type: 'warning',
          priority: 'high',
          status: 'active',
          target_audience: 'all',
          created_by: 'Sistema',
          created_at: new Date().toISOString(),
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          read_by: []
        },
        {
          id: '2',
          title: 'Nueva versión del portal disponible',
          message: 'Se ha lanzado una nueva versión del portal con mejoras en la gestión de tickets y nuevas funcionalidades de reportes.',
          type: 'success',
          priority: 'medium',
          status: 'active',
          target_audience: 'all',
          created_by: 'Karedesk',
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          read_by: ['user1', 'user2']
        },
        {
          id: '3',
          title: 'Problema crítico resuelto',
          message: 'El problema de conectividad que afectaba a algunos usuarios ha sido completamente resuelto. Todos los servicios funcionan con normalidad.',
          type: 'info',
          priority: 'high',
          status: 'active',
          target_audience: 'technicians',
          created_by: 'IT Support',
          created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          read_by: []
        }
      ];

      setNotifications(mockNotifications);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar las notificaciones",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const createNotification = async () => {
    try {
      const notification: Notification = {
        id: Date.now().toString(),
        ...newNotification,
        status: 'active',
        created_by: 'Admin',
        created_at: new Date().toISOString(),
        read_by: []
      };

      setNotifications(prev => [notification, ...prev]);

      toast({
        title: "Éxito",
        description: "Notificación creada correctamente",
      });

      setIsCreateDialogOpen(false);
      setNewNotification({
        title: '',
        message: '',
        type: 'info',
        priority: 'medium',
        target_audience: 'all',
        expires_at: ''
      });
    } catch (error) {
      console.error('Error creating notification:', error);
      toast({
        title: "Error",
        description: "No se pudo crear la notificación",
        variant: "destructive"
      });
    }
  };

  const dismissNotification = async (notificationId: string) => {
    try {
      setNotifications(prev => 
        prev.map(notif => 
          notif.id === notificationId 
            ? { ...notif, status: 'dismissed' as const }
            : notif
        )
      );

      toast({
        title: "Notificación descartada",
        description: "La notificación ha sido marcada como descartada",
      });
    } catch (error) {
      console.error('Error dismissing notification:', error);
    }
  };

  const deleteNotification = async (notificationId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta notificación?')) {
      return;
    }

    try {
      setNotifications(prev => prev.filter(notif => notif.id !== notificationId));

      toast({
        title: "Notificación eliminada",
        description: "La notificación ha sido eliminada correctamente",
      });
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, []);

  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = typeFilter === 'all' || notification.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || notification.status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const getTypeConfig = (type: string) => {
    return notificationTypes.find(t => t.value === type) || notificationTypes[0];
  };

  const getPriorityConfig = (priority: string) => {
    return priorityLevels.find(p => p.value === priority) || priorityLevels[1];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Centro de Notificaciones</h2>
          <p className="text-gray-600">{filteredNotifications.length} notificaciones</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Nueva Notificación
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Crear Nueva Notificación</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Título</label>
                <Input
                  value={newNotification.title}
                  onChange={(e) => setNewNotification({...newNotification, title: e.target.value})}
                  placeholder="Título de la notificación"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Mensaje</label>
                <Textarea
                  value={newNotification.message}
                  onChange={(e) => setNewNotification({...newNotification, message: e.target.value})}
                  placeholder="Contenido del mensaje"
                  rows={3}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Tipo</label>
                <Select value={newNotification.type} onValueChange={(value: any) => setNewNotification({...newNotification, type: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {notificationTypes.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium">Prioridad</label>
                <Select value={newNotification.priority} onValueChange={(value: any) => setNewNotification({...newNotification, priority: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {priorityLevels.map(priority => (
                      <SelectItem key={priority.value} value={priority.value}>
                        {priority.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium">Audiencia</label>
                <Select value={newNotification.target_audience} onValueChange={(value: any) => setNewNotification({...newNotification, target_audience: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {audiences.map(audience => (
                      <SelectItem key={audience.value} value={audience.value}>
                        {audience.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium">Fecha de expiración (opcional)</label>
                <Input
                  type="datetime-local"
                  value={newNotification.expires_at}
                  onChange={(e) => setNewNotification({...newNotification, expires_at: e.target.value})}
                />
              </div>
              <Button onClick={createNotification} className="w-full">
                <Send className="h-4 w-4 mr-2" />
                Crear Notificación
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar notificaciones..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los tipos</SelectItem>
                {notificationTypes.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los estados</SelectItem>
                <SelectItem value="active">Activas</SelectItem>
                <SelectItem value="dismissed">Descartadas</SelectItem>
                <SelectItem value="expired">Expiradas</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Notifications List */}
      <div className="space-y-4">
        <AnimatePresence>
          {filteredNotifications.map((notification) => {
            const typeConfig = getTypeConfig(notification.type);
            const priorityConfig = getPriorityConfig(notification.priority);
            const Icon = typeConfig.icon;

            return (
              <motion.div
                key={notification.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                <Card className={`hover:shadow-lg transition-shadow ${
                  notification.priority === 'critical' ? 'border-red-500 border-2' : ''
                }`}>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className={`p-2 rounded-full ${typeConfig.color.replace('text-', 'bg-').replace('bg-', 'bg-').split(' ')[0]}`}>
                        <Icon className="h-5 w-5 text-white" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-1">
                              {notification.title}
                            </h3>
                            <p className="text-gray-600 mb-3">
                              {notification.message}
                            </p>
                            
                            <div className="flex items-center gap-3 text-sm text-gray-500">
                              <div className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                <span>{new Date(notification.created_at).toLocaleDateString()}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Users className="h-4 w-4" />
                                <span>{audiences.find(a => a.value === notification.target_audience)?.label}</span>
                              </div>
                              <span>Por: {notification.created_by}</span>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Badge className={typeConfig.color}>
                              {typeConfig.label}
                            </Badge>
                            <Badge className={priorityConfig.color}>
                              {priorityConfig.label}
                            </Badge>
                            <Badge variant={notification.status === 'active' ? 'default' : 'secondary'}>
                              {notification.status === 'active' ? 'Activa' : 
                               notification.status === 'dismissed' ? 'Descartada' : 'Expirada'}
                            </Badge>
                          </div>
                        </div>

                        {notification.expires_at && (
                          <div className="mt-3 p-2 bg-yellow-50 rounded-lg">
                            <p className="text-sm text-yellow-800">
                              Expira: {new Date(notification.expires_at).toLocaleDateString()}
                            </p>
                          </div>
                        )}

                        <div className="flex gap-2 mt-4">
                          {notification.status === 'active' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => dismissNotification(notification.id)}
                            >
                              <EyeOff className="h-4 w-4 mr-1" />
                              Descartar
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => deleteNotification(notification.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Eliminar
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {filteredNotifications.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No hay notificaciones
            </h3>
            <p className="text-gray-600">
              No se encontraron notificaciones que coincidan con los filtros seleccionados.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
