
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { User, Building2 } from 'lucide-react';
import { memo } from 'react';

interface EmployeeSectionProps {
  empleadoNombre: string;
  setEmpleadoNombre: (value: string) => void;
  empleadoCdsid: string;
  setEmpleadoCdsid: (value: string) => void;
  isSubmitting: boolean;
}

export const EmployeeSection = memo(function EmployeeSection({
  empleadoNombre,
  setEmpleadoNombre,
  empleadoCdsid,
  setEmpleadoCdsid,
  isSubmitting
}: EmployeeSectionProps) {
  return (
    <div className="bg-gradient-to-br from-gray-50 to-white p-8 border-b border-gray-100">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-sm">
            <User className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-2xl font-bold text-gray-900">Información del Empleado</h3>
            <p className="text-gray-600 mt-1">Datos del solicitante y identificación corporativa</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-3">
            <Label htmlFor="empleadoNombre" className="text-sm font-semibold text-gray-800 flex items-center gap-2">
              <User className="w-4 h-4 text-blue-600" />
              Nombre Completo del Empleado
              <span className="text-red-500">*</span>
            </Label>
            <Input
              id="empleadoNombre"
              value={empleadoNombre}
              onChange={(e) => setEmpleadoNombre(e.target.value)}
              placeholder="Escriba el nombre completo del empleado"
              className="h-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-blue-200 rounded-lg text-base"
              disabled={isSubmitting}
            />
          </div>
          
          <div className="space-y-3">
            <Label htmlFor="empleadoCdsid" className="text-sm font-semibold text-gray-800 flex items-center gap-2">
              <Building2 className="w-4 h-4 text-blue-600" />
              CDSID Corporativo
              <span className="text-red-500">*</span>
            </Label>
            <Input
              id="empleadoCdsid"
              value={empleadoCdsid}
              onChange={(e) => setEmpleadoCdsid(e.target.value)}
              placeholder="Identificador corporativo único"
              className="h-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-blue-200 rounded-lg text-base"
              disabled={isSubmitting}
            />
          </div>
        </div>
      </div>
    </div>
  );
});
