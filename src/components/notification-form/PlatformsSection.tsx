import React, { useCallback } from 'react';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Settings, AlertTriangle, CheckCircle2 } from 'lucide-react';
import { platforms } from './constants';

interface PlatformsSectionProps {
  selectedPlatforms: string[];
  onPlatformChange: (platform: string, checked: boolean) => void;
  isSubmitting: boolean;
}

export const PlatformsSection = React.memo(function PlatformsSection({ selectedPlatforms, onPlatformChange, isSubmitting }: PlatformsSectionProps) {
  const handleContainerClick = useCallback((platformName: string) => {
    if (isSubmitting) return;
    const isSelected = selectedPlatforms.includes(platformName);
    onPlatformChange(platformName, !isSelected);
  }, [selectedPlatforms, onPlatformChange, isSubmitting]);

  const handleCheckboxChange = useCallback((platformName: string, checked: boolean | string) => {
    if (isSubmitting) return;
    onPlatformChange(platformName, checked === true);
  }, [onPlatformChange, isSubmitting]);

  return (
    <div className="bg-gradient-to-br from-purple-50 to-white p-8 border-b border-gray-100">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-sm">
            <Settings className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-2xl font-bold text-gray-900">Plataformas sin Acceso</h3>
            <p className="text-gray-600 mt-1">Seleccione todas las herramientas a las que necesita acceso</p>
            <Badge className="mt-2 bg-red-100 text-red-700 border-red-200">
              <AlertTriangle className="w-3 h-3 mr-1" />
              Debe seleccionar al menos una plataforma
            </Badge>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {platforms.map((platform) => {
            const isSelected = selectedPlatforms.includes(platform.name);
            
            return (
              <div
                key={platform.name}
                className="flex items-center space-x-4 p-6 rounded-xl bg-white border-2 border-gray-200 hover:bg-gradient-to-r hover:from-purple-50 hover:to-blue-50 hover:border-purple-400 hover:shadow-lg transition-all duration-300 cursor-pointer group transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-4 focus:ring-purple-200 focus:border-purple-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="relative">
                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={(checked) => handleCheckboxChange(platform.name, checked)}
                    className="data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600 scale-110"
                    disabled={isSubmitting}
                  />
                </div>
                <div 
                  onClick={() => handleContainerClick(platform.name)}
                  className="flex items-center space-x-4 flex-1 cursor-pointer"
                >
                  <div className="p-3 rounded-xl bg-gradient-to-br from-purple-100 to-purple-50 group-hover:from-purple-200 group-hover:to-purple-100 group-active:from-purple-300 group-active:to-purple-200 transition-all duration-300 shadow-sm group-hover:shadow-md">
                    <platform.icon className="w-6 h-6 text-purple-600 group-hover:text-purple-700" />
                  </div>
                  <div className="flex-1 text-left">
                    <Label
                      className="text-base font-bold leading-none cursor-pointer group-hover:text-purple-700 transition-colors block mb-1"
                    >
                      {platform.name}
                    </Label>
                    <p className="text-sm text-gray-600 group-hover:text-gray-700">{platform.description}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        {selectedPlatforms.length > 0 && (
          <div className="mt-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 rounded-xl shadow-sm">
            <div className="flex items-center gap-3 mb-4">
              <CheckCircle2 className="h-6 w-6 text-green-600" />
              <span className="text-lg font-bold text-green-800">
                Plataformas seleccionadas ({selectedPlatforms.length}):
              </span>
            </div>
            <div className="flex flex-wrap gap-3">
              {selectedPlatforms.map((platform) => (
                <Badge key={platform} className="bg-green-100 text-green-800 border-green-300 px-4 py-2 text-sm font-semibold shadow-sm">
                  {platform}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
});
