
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Send, AlertTriangle } from 'lucide-react';

interface SubmitSectionProps {
  isSubmitting: boolean;
  isFormValid: boolean;
  selectedPlatforms: string[];
}

export function SubmitSection({ isSubmitting, isFormValid, selectedPlatforms }: SubmitSectionProps) {
  return (
    <>
      {/* Validation Warning */}
      {!isFormValid && (
        <div className="bg-red-50 border-t border-red-200 p-6">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0" />
              <div>
                <span className="text-sm font-semibold text-red-800 block">
                  Campos obligatorios pendientes
                </span>
                <span className="text-xs text-red-700">
                  Complete todos los campos marcados con (*) para enviar la solicitud
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Submit Button */}
      <div className="bg-gradient-to-br from-blue-50 to-white p-8 border-t border-blue-100">
        <div className="max-w-4xl mx-auto">
          <Button 
            type="submit" 
            disabled={isSubmitting || !isFormValid}
            className="w-full h-16 text-lg font-bold bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 active:from-blue-800 active:to-blue-900 text-white rounded-xl shadow-lg hover:shadow-xl active:shadow-md transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98] focus:ring-4 focus:ring-blue-300 focus:outline-none"
          >
            {isSubmitting ? (
              <>
                <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin mr-3"></div>
                Procesando Solicitud...
              </>
            ) : (
              <>
                <Send className="w-6 h-6 mr-3 transition-transform group-hover:translate-x-1" />
                Enviar Solicitud de Acceso
                {selectedPlatforms.length > 0 && (
                  <Badge className="ml-3 bg-white/20 text-white border-white/30 hover:bg-white/30 transition-colors">
                    {selectedPlatforms.length} plataforma{selectedPlatforms.length > 1 ? 's' : ''}
                  </Badge>
                )}
              </>
            )}
          </Button>
        </div>
      </div>
    </>
  );
}
