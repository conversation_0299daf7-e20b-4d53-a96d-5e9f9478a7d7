
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Headphones } from 'lucide-react';
import { markets } from './constants';
import { memo } from 'react';

interface MarketSectionProps {
  region: string;
  setRegion: (value: string) => void;
  isSubmitting: boolean;
}

export const MarketSection = memo(function MarketSection({ region, setRegion, isSubmitting }: MarketSectionProps) {
  return (
    <div className="bg-gradient-to-br from-green-50 to-white p-8 border-b border-gray-100">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <div className="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-sm">
            <Headphones className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-2xl font-bold text-gray-900">Mercado del Contact Center</h3>
            <p className="text-gray-600 mt-1">Mercado específico donde opera el empleado</p>
          </div>
        </div>
        
        <div className="space-y-3">
          <Label htmlFor="region" className="text-sm font-semibold text-gray-800 flex items-center gap-2">
            <Headphones className="w-4 h-4 text-green-600" />
            Mercado del Contact Center
            <span className="text-red-500">*</span>
          </Label>
          <Select value={region} onValueChange={setRegion} disabled={isSubmitting}>
            <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-green-500 focus:ring-green-200 rounded-lg text-base">
              <SelectValue placeholder="Seleccione el mercado del Contact Center" />
            </SelectTrigger>
            <SelectContent className="bg-white border-2 border-gray-200 shadow-xl rounded-xl">
              {markets.map((market) => (
                <SelectItem key={market.name} value={market.name} className="hover:bg-gray-50 focus:bg-gray-50 rounded-lg transition-colors py-3">
                  <div className="flex items-center gap-3">
                    <market.icon className="w-5 h-5 text-green-600" />
                    <div>
                      <span className="font-semibold">{market.name}</span>
                      <span className="text-xs text-gray-500 ml-2">({market.code})</span>
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
});
