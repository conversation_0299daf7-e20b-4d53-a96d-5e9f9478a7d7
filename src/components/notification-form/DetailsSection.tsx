
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Star, Zap, FileText } from 'lucide-react';
import { priorities } from './constants';
import { memo, useMemo } from 'react';

interface DetailsSectionProps {
  prioridad: 'baja' | 'media' | 'alta' | 'critica';
  setPrioridad: (value: 'baja' | 'media' | 'alta' | 'critica') => void;
  descripcion: string;
  setDescripcion: (value: string) => void;
  isSubmitting: boolean;
}

export const DetailsSection = memo(function DetailsSection({
  prioridad,
  setPrioridad,
  descripcion,
  setDescripcion,
  isSubmitting
}: DetailsSectionProps) {
  const selectedPriority = useMemo(() => 
    priorities.find(p => p.value === prioridad), 
    [prioridad]
  );

  const handlePriorityChange = (value: string) => {
    setPrioridad(value as 'baja' | 'media' | 'alta' | 'critica');
  };

  return (
    <div className="bg-gradient-to-br from-amber-50 to-white p-8">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <div className="p-3 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl shadow-sm">
            <Star className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-2xl font-bold text-gray-900">Detalles de la Solicitud</h3>
            <p className="text-gray-600 mt-1">Información adicional opcional sobre la solicitud</p>
            <Badge className="mt-2 bg-amber-100 text-amber-700 border-amber-200">
              <Star className="w-3 h-3 mr-1" />
              Campos opcionales
            </Badge>
          </div>
        </div>
        
        <div className="space-y-8">
          <div className="space-y-3">
            <Label htmlFor="prioridad" className="text-sm font-semibold text-gray-800 flex items-center gap-2">
              <Zap className="w-4 h-4 text-amber-600" />
              Nivel de Prioridad
              <Badge className="bg-gray-100 text-gray-600 text-xs">Opcional</Badge>
            </Label>
            <Select value={prioridad} onValueChange={handlePriorityChange} disabled={isSubmitting}>
              <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-amber-500 focus:ring-amber-200 rounded-lg text-base">
                <SelectValue placeholder="Seleccione el nivel de urgencia (opcional)" />
              </SelectTrigger>
              <SelectContent className="bg-white border-2 border-gray-200 shadow-xl rounded-xl">
                {priorities.map((p) => (
                  <SelectItem key={p.value} value={p.value} className="hover:bg-gray-50 focus:bg-gray-50 rounded-lg transition-colors py-3">
                    <div className="flex items-center gap-3">
                      <p.icon className={`w-5 h-5 ${p.value === 'alta' ? 'text-red-500' : p.value === 'media' ? 'text-amber-500' : 'text-green-500'}`} />
                      <div>
                        <span className="font-semibold">{p.label}</span>
                        <p className="text-xs text-gray-500 mt-1">{p.description}</p>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {selectedPriority && (
              <div className="p-3 rounded-lg border-l-4 border-amber-300 bg-amber-50">
                <div className="flex items-center gap-2">
                  <selectedPriority.icon className={`w-4 h-4 ${selectedPriority.value === 'alta' ? 'text-red-500' : selectedPriority.value === 'media' ? 'text-amber-500' : 'text-green-500'}`} />
                  <span className="text-sm font-medium text-gray-700">
                    {selectedPriority.description}
                  </span>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-3">
            <Label htmlFor="descripcion" className="text-sm font-semibold text-gray-800 flex items-center gap-2">
              <FileText className="w-4 h-4 text-amber-600" />
              Descripción Detallada del Problema
              <Badge className="bg-gray-100 text-gray-600 text-xs">Opcional</Badge>
            </Label>
            <Textarea
              id="descripcion"
              value={descripcion}
              onChange={(e) => setDescripcion(e.target.value)}
              placeholder="Proporcione detalles adicionales sobre el problema de acceso, errores específicos que aparecen, o cualquier información relevante que pueda ayudar en la resolución..."
              className="min-h-[120px] border-2 border-gray-200 focus:border-amber-500 focus:ring-amber-200 rounded-lg text-base resize-none"
              disabled={isSubmitting}
              rows={4}
            />
          </div>
        </div>
      </div>
    </div>
  );
});
