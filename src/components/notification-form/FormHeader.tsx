
import { <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Send, CheckCircle2, Lock } from 'lucide-react';

export function FormHeader() {
  return (
    <CardHeader className="bg-gradient-to-br from-blue-50 via-white to-blue-50 border-b border-blue-100 py-8">
      <CardTitle className="text-center space-y-4">
        <div className="flex justify-center">
          <div className="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg">
            <Lock className="h-8 w-8 text-white" />
          </div>
        </div>
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Solicitud de Accesos
          </h2>
          <p className="text-lg text-gray-600 font-medium">
            Complete el formulario para solicitar accesos a plataformas corporativas
          </p>
        </div>
        <div className="flex justify-center gap-2">
          <Badge className="bg-blue-100 text-blue-700 border-blue-200">
            <Send className="w-3 h-3 mr-1" />
            Rápido
          </Badge>
          <Badge className="bg-green-100 text-green-700 border-green-200">
            <CheckCircle2 className="w-3 h-3 mr-1" />
            Seguro
          </Badge>
        </div>
      </CardTitle>
    </CardHeader>
  );
}
