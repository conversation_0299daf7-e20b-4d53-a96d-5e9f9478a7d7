import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Send, User, Clock } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface TicketComment {
  id: string;
  ticket_id: string;
  author: string;
  content: string;
  is_internal: boolean;
  created_at: string;
}

interface TicketCommentsProps {
  ticketId: string;
}

export const TicketComments = ({ ticketId }: TicketCommentsProps) => {
  const [newComment, setNewComment] = useState('');
  const [authorName, setAuthorName] = useState('');
  const queryClient = useQueryClient();

  const { data: comments = [], isLoading } = useQuery({
    queryKey: ['ticket-comments', ticketId],
    queryFn: async () => {
      // Using existing ticket_comments table structure
      const { data, error } = await supabase
        .from('ticket_comments')
        .select('*')
        .eq('ticket_id', ticketId)
        .order('created_at', { ascending: true });
      
      if (error) throw error;
      return data as TicketComment[];
    }
  });

  const addCommentMutation = useMutation({
    mutationFn: async (comment: { content: string; author: string }) => {
      const { error } = await supabase
        .from('ticket_comments')
        .insert([{
          ticket_id: ticketId,
          author: comment.author,
          content: comment.content,
          is_internal: false
        }]);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ticket-comments', ticketId] });
      setNewComment('');
      toast({
        title: "Comentario añadido",
        description: "Tu comentario ha sido añadido al ticket",
      });
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || !authorName.trim()) return;
    
    addCommentMutation.mutate({
      content: newComment,
      author: authorName
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Comentarios ({comments.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Existing Comments */}
        <div className="space-y-4">
          {isLoading ? (
            <div className="text-center py-4">Cargando comentarios...</div>
          ) : comments.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No hay comentarios aún</p>
          ) : (
            comments.map((comment) => (
              <div key={comment.id} className="border rounded-lg p-4 bg-gray-50">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <span className="font-medium text-gray-900">{comment.author}</span>
                    {comment.is_internal && (
                      <Badge variant="secondary" className="text-xs">Interno</Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <Clock className="h-3 w-3" />
                    {new Date(comment.created_at).toLocaleDateString('es-ES', {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
                <p className="text-gray-700 whitespace-pre-wrap">{comment.content}</p>
              </div>
            ))
          )}
        </div>

        {/* Add New Comment */}
        <div className="border-t pt-6">
          <h4 className="font-medium text-gray-900 mb-4">Añadir comentario</h4>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="author">Tu nombre</Label>
              <Input
                id="author"
                value={authorName}
                onChange={(e) => setAuthorName(e.target.value)}
                placeholder="Introduce tu nombre"
                required
              />
            </div>
            <div>
              <Label htmlFor="comment">Comentario</Label>
              <Textarea
                id="comment"
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Escribe tu comentario aquí..."
                rows={4}
                required
              />
            </div>
            <Button 
              type="submit" 
              disabled={addCommentMutation.isPending || !newComment.trim() || !authorName.trim()}
              className="w-full sm:w-auto"
            >
              {addCommentMutation.isPending ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                  Enviando...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Enviar Comentario
                </>
              )}
            </Button>
          </form>
        </div>
      </CardContent>
    </Card>
  );
};
