import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Settings, Save, User, AlertTriangle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface TicketStatusManagerProps {
  ticketId: string;
  currentStatus: string;
  currentPriority: string;
  assignedTo?: string;
}

export const TicketStatusManager = ({ 
  ticketId, 
  currentStatus, 
  currentPriority, 
  assignedTo 
}: TicketStatusManagerProps) => {
  const [status, setStatus] = useState(currentStatus);
  const [priority, setPriority] = useState(currentPriority);
  const [assignee, setAssignee] = useState(assignedTo || '');
  const [notes, setNotes] = useState('');
  const queryClient = useQueryClient();

  const updateTicketMutation = useMutation({
    mutationFn: async (updates: { status?: string; priority?: string; assigned_to?: string }) => {
      const { error } = await supabase
        .from('tickets')
        .update(updates)
        .eq('id', ticketId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ticket-details', ticketId] });
      queryClient.invalidateQueries({ queryKey: ['ticket-stats'] });
      toast({
        title: "Ticket actualizado",
        description: "Los cambios han sido guardados exitosamente",
      });
    }
  });

  const assignTicketMutation = useMutation({
    mutationFn: async (assignment: { assigned_to: string; notes: string }) => {
      // Simulate assignment since ticket_assignments table doesn't exist in types
      console.log('Assigning ticket:', { ticketId, ...assignment });
      
      // Update the ticket with assigned_to field
      const { error } = await supabase
        .from('tickets')
        .update({ assigned_to: assignment.assigned_to })
        .eq('id', ticketId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ticket-details', ticketId] });
      setNotes('');
      toast({
        title: "Ticket asignado",
        description: "El ticket ha sido asignado exitosamente",
      });
    }
  });

  const handleStatusUpdate = () => {
    const updates: any = {};
    
    if (status !== currentStatus) updates.status = status;
    if (priority !== currentPriority) updates.priority = priority;
    if (assignee !== assignedTo) updates.assigned_to = assignee;
    
    if (Object.keys(updates).length > 0) {
      updateTicketMutation.mutate(updates);
    }
  };

  const handleAssignment = () => {
    if (!assignee.trim()) return;
    
    assignTicketMutation.mutate({
      assigned_to: assignee,
      notes: notes
    });
  };

  const getStatusColor = (statusValue: string) => {
    switch (statusValue) {
      case 'pending': return 'text-blue-600';
      case 'in_progress': return 'text-yellow-600';
      case 'resolved': return 'text-green-600';
      case 'closed': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getPriorityColor = (priorityValue: string) => {
    switch (priorityValue) {
      case 'critical': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Gestión del Ticket
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status and Priority Update */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="status">Estado</Label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pendiente</SelectItem>
                <SelectItem value="in_progress">En Progreso</SelectItem>
                <SelectItem value="resolved">Resuelto</SelectItem>
                <SelectItem value="closed">Cerrado</SelectItem>
              </SelectContent>
            </Select>
            <p className={`text-xs mt-1 ${getStatusColor(status)}`}>
              Estado actual: {status.replace('_', ' ')}
            </p>
          </div>

          <div>
            <Label htmlFor="priority">Prioridad</Label>
            <Select value={priority} onValueChange={setPriority}>
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar prioridad" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Baja</SelectItem>
                <SelectItem value="medium">Media</SelectItem>
                <SelectItem value="high">Alta</SelectItem>
                <SelectItem value="critical">Crítica</SelectItem>
              </SelectContent>
            </Select>
            <p className={`text-xs mt-1 ${getPriorityColor(priority)}`}>
              Prioridad actual: {priority}
            </p>
          </div>

          <div>
            <Label htmlFor="assignee">Asignado a</Label>
            <Input
              id="assignee"
              value={assignee}
              onChange={(e) => setAssignee(e.target.value)}
              placeholder="CDSID del técnico"
            />
            <p className="text-xs mt-1 text-gray-500">
              {assignedTo ? `Asignado a: ${assignedTo}` : 'Sin asignar'}
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3">
          <Button 
            onClick={handleStatusUpdate}
            disabled={updateTicketMutation.isPending}
            className="flex items-center gap-2"
          >
            {updateTicketMutation.isPending ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                Actualizando...
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                Actualizar Ticket
              </>
            )}
          </Button>

          {assignee && assignee !== assignedTo && (
            <Button 
              onClick={handleAssignment}
              disabled={assignTicketMutation.isPending}
              variant="outline"
              className="flex items-center gap-2"
            >
              {assignTicketMutation.isPending ? (
                <>
                  <div className="w-4 h-4 border-2 border-gray-400/30 border-t-gray-600 rounded-full animate-spin"></div>
                  Asignando...
                </>
              ) : (
                <>
                  <User className="h-4 w-4" />
                  Asignar Ticket
                </>
              )}
            </Button>
          )}
        </div>

        {/* Assignment Notes */}
        {assignee && (
          <div>
            <Label htmlFor="notes">Notas de asignación</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Añadir notas sobre la asignación..."
              rows={3}
            />
          </div>
        )}

        {/* Priority Warning */}
        {priority === 'critical' && (
          <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <p className="text-sm text-red-700">
              Este ticket tiene prioridad crítica y requiere atención inmediata.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
