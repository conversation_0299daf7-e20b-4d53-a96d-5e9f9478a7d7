import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  BarChart3, 
  TrendingUp, 
  Download, 
  Calendar,
  Users,
  Ticket,
  Clock,
  CheckCircle,
  AlertTriangle,
  FileText,
  PieChart,
  Activity,
  Target,
  Zap,
  Award
} from 'lucide-react';

interface ReportData {
  ticketMetrics: {
    total: number;
    resolved: number;
    pending: number;
    avgResolutionTime: string;
    satisfactionScore: number;
  };
  userMetrics: {
    totalUsers: number;
    activeUsers: number;
    newUsers: number;
    topDepartments: Array<{ name: string; count: number; }>;
  };
  performanceMetrics: {
    systemUptime: string;
    avgResponseTime: string;
    errorRate: string;
    peakUsageHours: string;
  };
  trends: {
    ticketTrend: Array<{ date: string; count: number; }>;
    userActivityTrend: Array<{ date: string; active: number; }>;
  };
}

export const ReportsAnalytics: React.FC = () => {
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState<{from: Date, to: Date}>({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    to: new Date()
  });
  const [reportType, setReportType] = useState('overview');
  const { toast } = useToast();

  const fetchReportData = async () => {
    try {
      setLoading(true);
      
      // Fetch real data from Supabase
      const { data: tickets, error: ticketsError } = await supabase
        .from('tickets')
        .select('*')
        .gte('created_at', dateRange.from.toISOString())
        .lte('created_at', dateRange.to.toISOString());

      if (ticketsError) throw ticketsError;

      const { data: users, error: usersError } = await supabase
        .from('admin_users')
        .select('*');

      if (usersError) throw usersError;

      // Process the data
      const totalTickets = tickets?.length || 0;
      const resolvedTickets = tickets?.filter(t => t.status === 'resolved').length || 0;
      const pendingTickets = tickets?.filter(t => t.status === 'pending').length || 0;

      const mockReportData: ReportData = {
        ticketMetrics: {
          total: totalTickets,
          resolved: resolvedTickets,
          pending: pendingTickets,
          avgResolutionTime: '2.3 días',
          satisfactionScore: 4.2
        },
        userMetrics: {
          totalUsers: users?.length || 0,
          activeUsers: users?.filter(u => u.active).length || 0,
          newUsers: users?.filter(u => {
            const createdAt = new Date(u.created_at);
            return createdAt >= dateRange.from && createdAt <= dateRange.to;
          }).length || 0,
          topDepartments: [
            { name: 'IT', count: 12 },
            { name: 'Soporte', count: 8 },
            { name: 'Administración', count: 5 }
          ]
        },
        performanceMetrics: {
          systemUptime: '99.97%',
          avgResponseTime: '145ms',
          errorRate: '0.02%',
          peakUsageHours: '09:00 - 11:00'
        },
        trends: {
          ticketTrend: [
            { date: '2024-01-01', count: 12 },
            { date: '2024-01-02', count: 15 },
            { date: '2024-01-03', count: 8 },
            { date: '2024-01-04', count: 22 },
            { date: '2024-01-05', count: 18 }
          ],
          userActivityTrend: [
            { date: '2024-01-01', active: 45 },
            { date: '2024-01-02', active: 52 },
            { date: '2024-01-03', active: 38 },
            { date: '2024-01-04', active: 61 },
            { date: '2024-01-05', active: 55 }
          ]
        }
      };

      setReportData(mockReportData);
    } catch (error) {
      console.error('Error fetching report data:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar los datos del reporte",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async (format: 'csv' | 'pdf') => {
    try {
      toast({
        title: "Exportando reporte",
        description: `Generando archivo ${format.toUpperCase()}...`,
      });

      // Mock export functionality
      setTimeout(() => {
        toast({
          title: "Reporte exportado",
          description: `El reporte ha sido descargado en formato ${format.toUpperCase()}`,
        });
      }, 2000);
    } catch (error) {
      console.error('Error exporting report:', error);
      toast({
        title: "Error",
        description: "No se pudo exportar el reporte",
        variant: "destructive"
      });
    }
  };

  useEffect(() => {
    fetchReportData();
  }, [dateRange, reportType]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="text-center p-8">
        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          No hay datos disponibles
        </h3>
        <p className="text-gray-600">
          No se encontraron datos para el período seleccionado.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Reportes y Analytics</h2>
          <p className="text-gray-600">
            Análisis detallado del rendimiento del sistema
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => exportReport('csv')}>
            <Download className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => exportReport('pdf')}>
            <Download className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Período de tiempo
              </label>
              <DatePickerWithRange
                date={dateRange}
                onDateChange={setDateRange}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Tipo de reporte
              </label>
              <Select value={reportType} onValueChange={setReportType}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="overview">Vista general</SelectItem>
                  <SelectItem value="tickets">Tickets detallado</SelectItem>
                  <SelectItem value="users">Usuarios detallado</SelectItem>
                  <SelectItem value="performance">Rendimiento</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button onClick={fetchReportData} className="mt-6">
              <BarChart3 className="h-4 w-4 mr-2" />
              Actualizar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Total Tickets</p>
                  <p className="text-3xl font-bold">{reportData.ticketMetrics.total}</p>
                </div>
                <Ticket className="h-12 w-12 text-blue-200" />
              </div>
              <div className="mt-4 flex items-center">
                <TrendingUp className="h-4 w-4 mr-1" />
                <span className="text-sm">+12% vs mes anterior</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100">Tickets Resueltos</p>
                  <p className="text-3xl font-bold">{reportData.ticketMetrics.resolved}</p>
                </div>
                <CheckCircle className="h-12 w-12 text-green-200" />
              </div>
              <div className="mt-4 flex items-center">
                <Award className="h-4 w-4 mr-1" />
                <span className="text-sm">
                  {Math.round((reportData.ticketMetrics.resolved / reportData.ticketMetrics.total) * 100)}% 
                  tasa de resolución
                </span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Usuarios Activos</p>
                  <p className="text-3xl font-bold">{reportData.userMetrics.activeUsers}</p>
                </div>
                <Users className="h-12 w-12 text-purple-200" />
              </div>
              <div className="mt-4 flex items-center">
                <Activity className="h-4 w-4 mr-1" />
                <span className="text-sm">
                  {reportData.userMetrics.newUsers} nuevos este mes
                </span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">Tiempo Promedio</p>
                  <p className="text-3xl font-bold">{reportData.ticketMetrics.avgResolutionTime}</p>
                </div>
                <Clock className="h-12 w-12 text-orange-200" />
              </div>
              <div className="mt-4 flex items-center">
                <Target className="h-4 w-4 mr-1" />
                <span className="text-sm">Resolución de tickets</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Ticket Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Distribución de Estados
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-green-500 rounded"></div>
                  <span>Resueltos</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-semibold">{reportData.ticketMetrics.resolved}</span>
                  <Badge className="bg-green-100 text-green-800">
                    {Math.round((reportData.ticketMetrics.resolved / reportData.ticketMetrics.total) * 100)}%
                  </Badge>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                  <span>Pendientes</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-semibold">{reportData.ticketMetrics.pending}</span>
                  <Badge className="bg-yellow-100 text-yellow-800">
                    {Math.round((reportData.ticketMetrics.pending / reportData.ticketMetrics.total) * 100)}%
                  </Badge>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-blue-500 rounded"></div>
                  <span>En progreso</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-semibold">
                    {reportData.ticketMetrics.total - reportData.ticketMetrics.resolved - reportData.ticketMetrics.pending}
                  </span>
                  <Badge className="bg-blue-100 text-blue-800">
                    {Math.round(((reportData.ticketMetrics.total - reportData.ticketMetrics.resolved - reportData.ticketMetrics.pending) / reportData.ticketMetrics.total) * 100)}%
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Métricas de Rendimiento
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Uptime del Sistema</span>
                <Badge className="bg-green-100 text-green-800">
                  {reportData.performanceMetrics.systemUptime}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Tiempo de Respuesta</span>
                <Badge className="bg-blue-100 text-blue-800">
                  {reportData.performanceMetrics.avgResponseTime}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-600">Tasa de Errores</span>
                <Badge className="bg-green-100 text-green-800">
                  {reportData.performanceMetrics.errorRate}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-600">Horas Pico</span>
                <Badge className="bg-purple-100 text-purple-800">
                  {reportData.performanceMetrics.peakUsageHours}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Department Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Análisis por Departamento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reportData.userMetrics.topDepartments.map((dept, index) => (
              <div key={dept.name} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                    {index + 1}
                  </div>
                  <span className="font-medium">{dept.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full" 
                      style={{ width: `${(dept.count / reportData.userMetrics.totalUsers) * 100}%` }}
                    ></div>
                  </div>
                  <span className="font-semibold w-8 text-right">{dept.count}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Satisfaction Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Puntuación de Satisfacción
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-6">
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">
                {reportData.ticketMetrics.satisfactionScore}/5
              </div>
              <p className="text-gray-600">Puntuación promedio</p>
            </div>
            <div className="flex-1">
              <div className="space-y-2">
                {[5, 4, 3, 2, 1].map(rating => (
                  <div key={rating} className="flex items-center gap-2">
                    <span className="w-4 text-sm">{rating}</span>
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full" 
                        style={{ width: `${rating === 4 ? 60 : rating === 5 ? 30 : rating === 3 ? 8 : 2}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-8">
                      {rating === 4 ? '60%' : rating === 5 ? '30%' : rating === 3 ? '8%' : '2%'}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
