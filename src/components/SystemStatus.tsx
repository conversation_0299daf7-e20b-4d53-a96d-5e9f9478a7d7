import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  Activity, 
  Server, 
  Database, 
  Wifi, 
  Shield, 
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Monitor,
  HardDrive,
  Cpu,
  MemoryStick,
  Globe,
  Zap
} from 'lucide-react';

interface SystemMetric {
  name: string;
  value: string;
  status: 'healthy' | 'warning' | 'critical';
  trend: 'up' | 'down' | 'stable';
  description: string;
  lastUpdated: string;
}

interface ServiceStatus {
  name: string;
  status: 'online' | 'degraded' | 'offline';
  responseTime: number;
  uptime: string;
  lastCheck: string;
  description: string;
}

export const SystemStatus: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemMetric[]>([]);
  const [services, setServices] = useState<ServiceStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const { toast } = useToast();

  const fetchSystemStatus = async () => {
    try {
      setLoading(true);
      
      // Mock system metrics - in a real implementation, these would come from monitoring APIs
      const mockMetrics: SystemMetric[] = [
        {
          name: 'CPU Usage',
          value: '23%',
          status: 'healthy',
          trend: 'stable',
          description: 'Procesador funcionando dentro de parámetros normales',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'Memory Usage',
          value: '67%',
          status: 'warning',
          trend: 'up',
          description: 'Uso de memoria elevado pero dentro de límites aceptables',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'Disk Space',
          value: '45%',
          status: 'healthy',
          trend: 'stable',
          description: 'Espacio en disco disponible',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'Network Latency',
          value: '12ms',
          status: 'healthy',
          trend: 'down',
          description: 'Latencia de red óptima',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'Active Sessions',
          value: '24',
          status: 'healthy',
          trend: 'up',
          description: 'Sesiones de usuario activas',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'Error Rate',
          value: '0.02%',
          status: 'healthy',
          trend: 'stable',
          description: 'Tasa de errores muy baja',
          lastUpdated: new Date().toISOString()
        }
      ];

      const mockServices: ServiceStatus[] = [
        {
          name: 'Portal Web',
          status: 'online',
          responseTime: 145,
          uptime: '99.98%',
          lastCheck: new Date().toISOString(),
          description: 'Aplicación web principal'
        },
        {
          name: 'Base de Datos',
          status: 'online',
          responseTime: 23,
          uptime: '99.99%',
          lastCheck: new Date().toISOString(),
          description: 'Supabase PostgreSQL'
        },
        {
          name: 'API Gateway',
          status: 'online',
          responseTime: 67,
          uptime: '99.95%',
          lastCheck: new Date().toISOString(),
          description: 'Puerta de enlace de APIs'
        },
        {
          name: 'Autenticación',
          status: 'online',
          responseTime: 89,
          uptime: '99.97%',
          lastCheck: new Date().toISOString(),
          description: 'Sistema de autenticación'
        },
        {
          name: 'Notificaciones',
          status: 'degraded',
          responseTime: 234,
          uptime: '98.45%',
          lastCheck: new Date().toISOString(),
          description: 'Servicio de notificaciones (rendimiento reducido)'
        },
        {
          name: 'Almacenamiento',
          status: 'online',
          responseTime: 156,
          uptime: '99.92%',
          lastCheck: new Date().toISOString(),
          description: 'Sistema de archivos y almacenamiento'
        }
      ];

      setMetrics(mockMetrics);
      setServices(mockServices);
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Error fetching system status:', error);
      toast({
        title: "Error",
        description: "No se pudo obtener el estado del sistema",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'online':
        return 'bg-green-100 text-green-800';
      case 'warning':
      case 'degraded':
        return 'bg-yellow-100 text-yellow-800';
      case 'critical':
      case 'offline':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'online':
        return CheckCircle;
      case 'warning':
      case 'degraded':
        return AlertTriangle;
      case 'critical':
      case 'offline':
        return XCircle;
      default:
        return Activity;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return TrendingUp;
      case 'down':
        return TrendingDown;
      default:
        return Activity;
    }
  };

  const getMetricIcon = (name: string) => {
    switch (name.toLowerCase()) {
      case 'cpu usage':
        return Cpu;
      case 'memory usage':
        return MemoryStick;
      case 'disk space':
        return HardDrive;
      case 'network latency':
        return Wifi;
      case 'active sessions':
        return Monitor;
      case 'error rate':
        return AlertTriangle;
      default:
        return Activity;
    }
  };

  useEffect(() => {
    fetchSystemStatus();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchSystemStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  const overallHealth = services.every(s => s.status === 'online') && 
                       metrics.every(m => m.status === 'healthy') ? 'healthy' :
                       services.some(s => s.status === 'offline') || 
                       metrics.some(m => m.status === 'critical') ? 'critical' : 'warning';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Estado del Sistema</h2>
          <p className="text-gray-600">
            Última actualización: {lastRefresh.toLocaleTimeString()}
          </p>
        </div>
        <Button onClick={fetchSystemStatus} className="gap-2">
          <RefreshCw className="h-4 w-4" />
          Actualizar
        </Button>
      </div>

      {/* Overall Status */}
      <Card className={`border-2 ${
        overallHealth === 'healthy' ? 'border-green-200 bg-green-50' :
        overallHealth === 'warning' ? 'border-yellow-200 bg-yellow-50' :
        'border-red-200 bg-red-50'
      }`}>
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className={`p-3 rounded-full ${
              overallHealth === 'healthy' ? 'bg-green-500' :
              overallHealth === 'warning' ? 'bg-yellow-500' :
              'bg-red-500'
            }`}>
              <Activity className="h-8 w-8 text-white" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900">
                Sistema {overallHealth === 'healthy' ? 'Operativo' : 
                        overallHealth === 'warning' ? 'Con Advertencias' : 'Con Problemas'}
              </h3>
              <p className="text-gray-600">
                {overallHealth === 'healthy' ? 'Todos los servicios funcionan correctamente' :
                 overallHealth === 'warning' ? 'Algunos servicios tienen problemas menores' :
                 'Se detectaron problemas críticos que requieren atención'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Metrics */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Métricas del Sistema</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {metrics.map((metric, index) => {
            const StatusIcon = getStatusIcon(metric.status);
            const TrendIcon = getTrendIcon(metric.trend);
            const MetricIcon = getMetricIcon(metric.name);

            return (
              <motion.div
                key={metric.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <MetricIcon className="h-5 w-5 text-gray-600" />
                        <span className="font-medium text-gray-900">{metric.name}</span>
                      </div>
                      <Badge className={getStatusColor(metric.status)}>
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {metric.status}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold text-gray-900">{metric.value}</span>
                      <div className={`flex items-center gap-1 ${
                        metric.trend === 'up' ? 'text-red-600' :
                        metric.trend === 'down' ? 'text-green-600' :
                        'text-gray-600'
                      }`}>
                        <TrendIcon className="h-4 w-4" />
                        <span className="text-sm">{metric.trend}</span>
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600 mt-2">{metric.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Services Status */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Estado de Servicios</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {services.map((service, index) => {
            const StatusIcon = getStatusIcon(service.status);

            return (
              <motion.div
                key={service.name}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <Server className="h-5 w-5 text-gray-600" />
                        <div>
                          <h4 className="font-semibold text-gray-900">{service.name}</h4>
                          <p className="text-sm text-gray-600">{service.description}</p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(service.status)}>
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {service.status}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Respuesta</span>
                        <p className="font-semibold">{service.responseTime}ms</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Uptime</span>
                        <p className="font-semibold">{service.uptime}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Última verificación</span>
                        <p className="font-semibold">
                          {new Date(service.lastCheck).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Información del Sistema
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Versión</h4>
              <p className="text-gray-600">Portal v2.1.0</p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Entorno</h4>
              <p className="text-gray-600">Producción</p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Región</h4>
              <p className="text-gray-600">EU-West-1</p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Proveedor</h4>
              <p className="text-gray-600">Karedesk</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
