import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
}

class ErrorBoundary extends Component<Props, State> {
  public override state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    // Generate unique error ID for tracking
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return { 
      hasError: true, 
      error,
      errorId
    };
  }

  public override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });

    // In production, you could send this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      this.logErrorToService(error, errorInfo);
    }
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // TODO: Implement error logging to external service (Sentry, LogRocket, etc.)
    console.log('Error would be logged to external service:', {
      error: error.toString(),
      errorInfo: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    });
  };

  private handleReset = () => {
    this.setState({ 
      hasError: false, 
      error: undefined, 
      errorInfo: undefined,
      errorId: undefined 
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleReportError = () => {
    if (this.state.errorId) {
      // Create mailto link with error details
      const subject = encodeURIComponent(`Error Report - Ford Access Alert Portal [${this.state.errorId}]`);
      const body = encodeURIComponent(`
Error ID: ${this.state.errorId}
Timestamp: ${new Date().toISOString()}
URL: ${window.location.href}
User Agent: ${navigator.userAgent}

Error Details:
${this.state.error?.toString() || 'Unknown error'}

Component Stack:
${this.state.errorInfo?.componentStack || 'No component stack available'}

Please describe what you were doing when this error occurred:
[Your description here]
      `);
      
      window.location.href = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    }
  };

  public override render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
          <Card className="max-w-2xl w-full border-red-200 shadow-xl">
            <CardHeader className="text-center pb-6">
              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="w-8 h-8 text-red-600" />
              </div>
              <CardTitle className="text-2xl text-red-800">
                ¡Oops! Algo salió mal
              </CardTitle>
              <p className="text-gray-600 mt-2">
                Se produjo un error inesperado en la aplicación. Puedes intentar recargar la página o contactar al soporte técnico.
              </p>
              {this.state.errorId && (
                <p className="text-sm text-gray-500 mt-2 font-mono bg-gray-100 px-2 py-1 rounded">
                  ID de Error: {this.state.errorId}
                </p>
              )}
            </CardHeader>
            
            <CardContent className="space-y-6">
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="bg-gray-100 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-800 mb-2">Información del Error (Desarrollo):</h4>
                  <pre className="text-sm text-gray-600 overflow-auto max-h-40 whitespace-pre-wrap">
                    {this.state.error.toString()}
                    {this.state.errorInfo?.componentStack}
                  </pre>
                </div>
              )}
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  onClick={this.handleReset}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Intentar de nuevo
                </Button>
                
                <Button 
                  onClick={this.handleReload}
                  className="flex items-center gap-2 bg-red-600 hover:bg-red-700"
                >
                  <RefreshCw className="w-4 h-4" />
                  Recargar página
                </Button>

                <Button 
                  onClick={this.handleReportError}
                  variant="secondary"
                  className="flex items-center gap-2"
                >
                  <AlertTriangle className="w-4 h-4" />
                  Reportar Error
                </Button>
              </div>
              
              <div className="text-center text-sm text-gray-500">
                <p>Si el problema persiste, contacta al equipo de soporte IT del CX Valencia HUB</p>
                <p className="mt-1">📧 <EMAIL> | 📞 +34 XXX XXX XXX</p>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary; 