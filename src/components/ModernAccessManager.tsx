import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  RefreshCw, 
  Bell, 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  X,
  User,
  Activity
} from 'lucide-react';

interface AccessRequest {
  id: string;
  empleado_nombre: string;
  empleado_cdsid: string;
  region: string;
  plataformas_faltantes: string[];
  descripcion: string;
  prioridad: string;
  estado: string;
  created_at: string;
  updated_at: string;
}

export const ModernAccessManager = () => {
  const [activeTab, setActiveTab] = useState('pendiente');
  const [searchQuery, setSearchQuery] = useState('');
  const [newRequests, setNewRequests] = useState<string[]>([]);
  const { toast } = useToast();

  // Obtener solicitudes
  const { data: requests = [], isLoading, refetch } = useQuery({
    queryKey: ['access-requests'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('notificaciones_acceso')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as AccessRequest[];
    },
    refetchInterval: 30000,
    refetchOnWindowFocus: true,
  });

  // Filtrar solicitudes
  const filteredRequests = requests.filter(request => {
    const matchesTab = activeTab === 'todos' || request.estado === activeTab;
    const matchesSearch = !searchQuery || 
      request.empleado_nombre.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.empleado_cdsid.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesTab && matchesSearch;
  });

  // Actualizar estado de solicitud
  const updateRequestStatus = async (id: string, newStatus: string) => {
    try {
      await supabase
        .from('notificaciones_acceso')
        .update({ estado: newStatus, updated_at: new Date().toISOString() })
        .eq('id', id);
      
      await refetch();
      
      toast({
        title: "✅ Estado actualizado",
        description: `La solicitud se ha marcado como ${newStatus}`,
      });
    } catch (error) {
      toast({
        title: "❌ Error",
        description: "No se pudo actualizar el estado",
        variant: "destructive",
      });
    }
  };

  // Contar solicitudes por estado
  const statusCounts = requests.reduce((acc, req) => {
    acc[req.estado] = (acc[req.estado] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="space-y-6">
      {/* Header con controles */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestión de Solicitudes de Acceso</h2>
          <p className="text-gray-600">Administra las solicitudes de acceso a plataformas</p>
        </div>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          
          {statusCounts.pendiente > 0 && (
            <Badge variant="destructive" className="animate-pulse">
              <Bell className="h-3 w-3 mr-1" />
              {statusCounts.pendiente} pendientes
            </Badge>
          )}
        </div>
      </div>

      {/* Filtros */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Buscar por nombre o CDSID..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="pendiente">
              Pendientes {statusCounts.pendiente ? `(${statusCounts.pendiente})` : ''}
            </TabsTrigger>
            <TabsTrigger value="en_proceso">
              En Proceso {statusCounts.en_proceso ? `(${statusCounts.en_proceso})` : ''}
            </TabsTrigger>
            <TabsTrigger value="completado">
              Completadas {statusCounts.completado ? `(${statusCounts.completado})` : ''}
            </TabsTrigger>
            <TabsTrigger value="todos">
              Todas ({requests.length})
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Lista de solicitudes */}
      <div className="space-y-4">
        <AnimatePresence>
          {filteredRequests.map((request) => (
            <motion.div
              key={request.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className={`border-l-4 ${
                request.prioridad === 'alta' ? 'border-red-500 bg-red-50' :
                request.prioridad === 'media' ? 'border-yellow-500 bg-yellow-50' :
                'border-blue-500 bg-blue-50'
              } hover:shadow-lg transition-shadow`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <User className="h-5 w-5" />
                      {request.empleado_nombre}
                      <span className="text-sm text-gray-500">({request.empleado_cdsid})</span>
                    </CardTitle>
                    
                    <div className="flex items-center gap-2">
                      {/* Badge de prioridad */}
                      <Badge variant={
                        request.prioridad === 'alta' ? 'destructive' :
                        request.prioridad === 'media' ? 'secondary' : 'outline'
                      }>
                        {request.prioridad === 'alta' && <AlertTriangle className="h-3 w-3 mr-1" />}
                        {request.prioridad.toUpperCase()}
                      </Badge>
                      
                      {/* Badge de estado */}
                      <Badge variant={
                        request.estado === 'pendiente' ? 'outline' :
                        request.estado === 'en_proceso' ? 'secondary' :
                        request.estado === 'completado' ? 'default' : 'destructive'
                      }>
                        {request.estado === 'pendiente' && <Clock className="h-3 w-3 mr-1" />}
                        {request.estado === 'en_proceso' && <Activity className="h-3 w-3 mr-1" />}
                        {request.estado === 'completado' && <CheckCircle className="h-3 w-3 mr-1" />}
                        {request.estado === 'rechazado' && <X className="h-3 w-3 mr-1" />}
                        {request.estado.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  {/* Plataformas solicitadas */}
                  <div className="mb-3">
                    <p className="text-sm font-medium text-gray-700 mb-2">Plataformas solicitadas:</p>
                    <div className="flex flex-wrap gap-1">
                      {request.plataformas_faltantes?.map((plataforma, i) => (
                        <Badge key={i} variant="outline" className="text-xs">
                          {plataforma}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  {/* Descripción */}
                  {request.descripcion && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-700 mb-1">Descripción:</p>
                      <p className="text-sm text-gray-600 bg-white p-2 rounded border">
                        {request.descripcion}
                      </p>
                    </div>
                  )}
                  
                  {/* Botones de acción */}
                  <div className="flex flex-wrap gap-2">
                    {request.estado === 'pendiente' && (
                      <>
                        <Button 
                          size="sm"
                          onClick={() => updateRequestStatus(request.id, 'en_proceso')}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          <Activity className="h-4 w-4 mr-1" />
                          Iniciar Proceso
                        </Button>
                        <Button 
                          variant="outline"
                          size="sm"
                          onClick={() => updateRequestStatus(request.id, 'rechazado')}
                          className="border-red-300 text-red-600 hover:bg-red-50"
                        >
                          <X className="h-4 w-4 mr-1" />
                          Rechazar
                        </Button>
                      </>
                    )}
                    
                    {request.estado === 'en_proceso' && (
                      <>
                        <Button 
                          size="sm"
                          onClick={() => updateRequestStatus(request.id, 'completado')}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Completar
                        </Button>
                        <Button 
                          variant="outline"
                          size="sm"
                          onClick={() => updateRequestStatus(request.id, 'rechazado')}
                          className="border-red-300 text-red-600 hover:bg-red-50"
                        >
                          <X className="h-4 w-4 mr-1" />
                          Rechazar
                        </Button>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
        
        {filteredRequests.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <User className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No hay solicitudes</h3>
            <p className="text-gray-600">
              {searchQuery ? 'No se encontraron solicitudes que coincidan con tu búsqueda.' : 'No hay solicitudes de acceso en este momento.'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
