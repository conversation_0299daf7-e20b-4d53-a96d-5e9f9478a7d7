import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { motion, AnimatePresence } from 'framer-motion';
import bcrypt from 'bcryptjs';
import {
  Users,
  UserPlus,
  Shield,
  Edit,
  Trash2,
  Search,
  RefreshCw,
  CheckCircle,
  XCircle,
  Crown,
  AlertTriangle,
  Copy,
  Download,
  Eye,
  EyeOff,
  Mail,
  Calendar,
  Activity,
  Filter,
  MoreVertical
} from 'lucide-react';

interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'superadmin' | 'admin';
  active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

interface UserFormData {
  email: string;
  name: string;
  role: 'superadmin' | 'admin';
  password: string;
  confirmPassword: string;
  active: boolean;
}

const SimpleUserManagement = () => {
  console.log('🚀 SimpleUserManagement component is rendering...');
  const { toast } = useToast();
  const { user: currentUser } = useAuth();
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<AdminUser | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  
  const [formData, setFormData] = useState<UserFormData>({
    email: '',
    name: '',
    role: 'admin',
    password: '',
    confirmPassword: '',
    active: true
  });

  // Log current user from AuthContext
  useEffect(() => {
    console.log('🔍 SimpleUserManagement - Current user from AuthContext:', currentUser);
  }, [currentUser]);

  // Fetch users
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('admin_users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      const processedUsers = (data || []).map(user => ({
        ...user,
        role: user.role as 'superadmin' | 'admin'
      }));
      
      setUsers(processedUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar los usuarios",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter users
  useEffect(() => {
    let filtered = users;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Role filter
    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter);
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(user =>
        statusFilter === 'active' ? user.active : !user.active
      );
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, roleFilter, statusFilter]);

  useEffect(() => {
    fetchUsers();
  }, []);

  // Validate email format
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Create user
  const createUser = async () => {
    if (!formData.email || !formData.name || !formData.password) {
      toast({
        title: "Error",
        description: "Todos los campos son obligatorios",
        variant: "destructive"
      });
      return;
    }

    if (!validateEmail(formData.email)) {
      toast({
        title: "Error",
        description: "El formato del email no es válido",
        variant: "destructive"
      });
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      toast({
        title: "Error",
        description: "Las contraseñas no coinciden",
        variant: "destructive"
      });
      return;
    }

    if (formData.password.length < 8) {
      toast({
        title: "Error",
        description: "La contraseña debe tener al menos 8 caracteres",
        variant: "destructive"
      });
      return;
    }

    try {
      setLoading(true);
      
      // Check if email already exists
      const { data: existingUser } = await supabase
        .from('admin_users')
        .select('email')
        .eq('email', formData.email.toLowerCase())
        .single();

      if (existingUser) {
        toast({
          title: "Error",
          description: "Ya existe un usuario con este email",
          variant: "destructive"
        });
        return;
      }

      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(formData.password, saltRounds);

      // Create user
      const { error } = await supabase
        .from('admin_users')
        .insert({
          email: formData.email.toLowerCase(),
          name: formData.name,
          role: formData.role,
          password_hash: hashedPassword,
          active: formData.active
        });

      if (error) throw error;

      toast({
        title: "✅ Usuario creado exitosamente",
        description: `${formData.name} ha sido agregado como ${formData.role}`,
      });

      setIsCreateDialogOpen(false);
      resetForm();
      fetchUsers();
    } catch (error) {
      console.error('Error creating user:', error);
      toast({
        title: "Error",
        description: "No se pudo crear el usuario. Intenta nuevamente.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Update user
  const updateUser = async () => {
    if (!selectedUser) return;

    if (!formData.name || !formData.email) {
      toast({
        title: "Error",
        description: "El nombre y email son obligatorios",
        variant: "destructive"
      });
      return;
    }

    if (!validateEmail(formData.email)) {
      toast({
        title: "Error",
        description: "El formato del email no es válido",
        variant: "destructive"
      });
      return;
    }

    try {
      setLoading(true);
      
      const updateData: any = {
        name: formData.name,
        email: formData.email.toLowerCase(),
        role: formData.role,
        active: formData.active,
        updated_at: new Date().toISOString()
      };

      // Only update password if provided
      if (formData.password) {
        if (formData.password !== formData.confirmPassword) {
          toast({
            title: "Error",
            description: "Las contraseñas no coinciden",
            variant: "destructive"
          });
          return;
        }

        if (formData.password.length < 8) {
          toast({
            title: "Error",
            description: "La contraseña debe tener al menos 8 caracteres",
            variant: "destructive"
          });
          return;
        }

        const saltRounds = 12;
        updateData.password_hash = await bcrypt.hash(formData.password, saltRounds);
      }

      const { error } = await supabase
        .from('admin_users')
        .update(updateData)
        .eq('id', selectedUser.id);

      if (error) throw error;

      // If email was updated, also update it in auth.users to keep them synchronized
      if (formData.email.toLowerCase() !== selectedUser.email) {
        console.log('📧 Email changed, updating auth.users as well...');
        const { error: authError } = await supabase.rpc('update_user_email', {
          user_id: selectedUser.id,
          new_email: formData.email.toLowerCase()
        });

        if (authError) {
          console.warn('⚠️ Could not update auth.users email:', authError);
          // Don't throw error as the main update succeeded
        } else {
          console.log('✅ Successfully updated email in both tables');
        }
      }

      toast({
        title: "✅ Usuario actualizado",
        description: `${formData.name} ha sido actualizado exitosamente`,
      });

      setIsEditDialogOpen(false);
      setSelectedUser(null);
      resetForm();
      fetchUsers();
    } catch (error) {
      console.error('Error updating user:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar el usuario. Intenta nuevamente.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (user: AdminUser) => {
    if (user.id === currentUser?.id) {
      toast({
        title: "Error",
        description: "No puedes eliminar tu propio usuario",
        variant: "destructive"
      });
      return;
    }
    setUserToDelete(user);
    setIsDeleteDialogOpen(true);
  };

  // Delete user
  const deleteUser = async () => {
    if (!userToDelete) return;

    try {
      setLoading(true);

      const { error } = await supabase
        .from('admin_users')
        .delete()
        .eq('id', userToDelete.id);

      if (error) throw error;

      toast({
        title: "✅ Usuario eliminado",
        description: `${userToDelete.name} ha sido eliminado del sistema`,
      });

      setIsDeleteDialogOpen(false);
      setUserToDelete(null);
      fetchUsers();
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: "Error",
        description: "No se pudo eliminar el usuario. Intenta nuevamente.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Export users to CSV
  const exportUsers = () => {
    const csvContent = [
      ['Nombre', 'Email', 'Rol', 'Estado', 'Fecha Creación'],
      ...users.map(user => [
        user.name,
        user.email,
        user.role === 'superadmin' ? 'Super Administrador' : 'Administrador',
        user.active ? 'Activo' : 'Inactivo',
        new Date(user.created_at).toLocaleDateString('es-ES')
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `usuarios_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "✅ Exportación completada",
      description: "Los usuarios han sido exportados a CSV",
    });
  };

  const resetForm = () => {
    setFormData({
      email: '',
      name: '',
      role: 'admin',
      password: '',
      confirmPassword: '',
      active: true
    });
  };

  const openEditDialog = (user: AdminUser) => {
    setSelectedUser(user);
    setFormData({
      email: user.email,
      name: user.name,
      role: user.role,
      password: '',
      confirmPassword: '',
      active: user.active
    });
    setIsEditDialogOpen(true);
  };

  const getRoleIcon = (role: string) => {
    return role === 'superadmin' ? Crown : Shield;
  };

  const getRoleColor = (role: string) => {
    return role === 'superadmin' 
      ? 'bg-purple-100 text-purple-800 border-purple-200'
      : 'bg-blue-100 text-blue-800 border-blue-200';
  };

  const canManageUser = (targetUser: AdminUser) => {
    if (!currentUser) return false;
    if (currentUser.role === 'superadmin') return true;
    return false;
  };

  const canCreateUser = () => {
    const canCreate = currentUser?.role === 'superadmin' || currentUser?.role === 'admin';
    console.log('🔐 SimpleUserManagement - canCreateUser:', canCreate, 'currentUser:', currentUser);
    return canCreate;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando usuarios...</p>
          <p className="text-xs text-gray-400 mt-2">SimpleUserManagement Component</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center gap-3">
            <motion.div
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Users className="h-8 w-8 text-blue-600" />
            </motion.div>
            Gestión de Usuarios
          </h2>
          <p className="text-gray-600 mt-2 flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Administra usuarios, roles y permisos del sistema
          </p>
          <div className="flex items-center gap-4 mt-2">
            <Badge variant="outline" className="text-xs">
              {users.length} usuarios totales
            </Badge>
            <Badge variant="outline" className="text-xs">
              {users.filter(u => u.active).length} activos
            </Badge>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              onClick={exportUsers}
              variant="outline"
              className="flex items-center gap-2"
              disabled={users.length === 0}
            >
              <Download className="h-4 w-4" />
              Exportar CSV
            </Button>
          </motion.div>

          {canCreateUser() && (
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                    <UserPlus className="h-4 w-4" />
                    Crear Usuario
                  </Button>
                </motion.div>
              </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Crear Nuevo Usuario</DialogTitle>
                <DialogDescription>
                  Completa los campos para crear un nuevo usuario administrador.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="name">Nombre Completo</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Nombre del usuario"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="role">Rol</Label>
                  <Select value={formData.role} onValueChange={(value: any) => setFormData(prev => ({ ...prev, role: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {currentUser?.role === 'superadmin' && (
                        <SelectItem value="superadmin">Super Administrador</SelectItem>
                      )}
                      <SelectItem value="admin">Administrador</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password">Contraseña</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                      placeholder="Mínimo 8 caracteres"
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirmar Contraseña</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      value={formData.confirmPassword}
                      onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      placeholder="Repetir contraseña"
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="active"
                    checked={formData.active}
                    onChange={(e) => setFormData(prev => ({ ...prev, active: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor="active">Usuario activo</Label>
                </div>
                
                <div className="flex gap-2 pt-4">
                  <Button onClick={createUser} className="flex-1" disabled={loading}>
                    {loading ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : null}
                    Crear Usuario
                  </Button>
                  <Button variant="outline" onClick={() => {
                    setIsCreateDialogOpen(false);
                    resetForm();
                  }}>
                    Cancelar
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
        </div>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card className="border-0 shadow-lg bg-gradient-to-r from-white to-gray-50">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              <div className="flex-1 w-full">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Buscar por nombre o email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-12 border-gray-200 focus:border-blue-500 transition-colors"
                  />
                </div>
              </div>

              <div className="flex gap-3 w-full lg:w-auto">
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-full lg:w-40 h-12">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Rol" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos los roles</SelectItem>
                    <SelectItem value="superadmin">Super Admin</SelectItem>
                    <SelectItem value="admin">Administrador</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full lg:w-40 h-12">
                    <Activity className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Estado" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos</SelectItem>
                    <SelectItem value="active">Activos</SelectItem>
                    <SelectItem value="inactive">Inactivos</SelectItem>
                  </SelectContent>
                </Select>

                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button onClick={fetchUsers} variant="outline" className="h-12 px-6">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Actualizar
                  </Button>
                </motion.div>
              </div>
            </div>

            {(searchTerm || roleFilter !== 'all' || statusFilter !== 'all') && (
              <div className="mt-4 flex items-center gap-2 text-sm text-gray-600">
                <span>Mostrando {filteredUsers.length} de {users.length} usuarios</span>
                {(searchTerm || roleFilter !== 'all' || statusFilter !== 'all') && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSearchTerm('');
                      setRoleFilter('all');
                      setStatusFilter('all');
                    }}
                    className="text-xs"
                  >
                    Limpiar filtros
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Users List */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          <AnimatePresence>
            {filteredUsers.map((user, index) => {
              const RoleIcon = getRoleIcon(user.role);
              const isCurrentUser = user.id === currentUser?.id;

              return (
                <motion.div
                  key={user.id}
                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -20, scale: 0.95 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                >
                  <Card className={`hover:shadow-xl transition-all duration-300 border-0 shadow-lg ${
                    isCurrentUser
                      ? 'ring-2 ring-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50'
                      : 'bg-gradient-to-br from-white to-gray-50'
                  }`}>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <motion.div
                            className={`p-3 rounded-xl ${getRoleColor(user.role)} shadow-md`}
                            whileHover={{ scale: 1.1, rotate: 5 }}
                            transition={{ type: "spring", stiffness: 300 }}
                          >
                            <RoleIcon className="h-5 w-5" />
                          </motion.div>
                          <div>
                            <h3 className="font-bold text-gray-900 flex items-center gap-2">
                              {user.name}
                              {isCurrentUser && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  transition={{ type: "spring", stiffness: 300 }}
                                >
                                  <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">
                                    Tú
                                  </Badge>
                                </motion.div>
                              )}
                            </h3>
                            <p className="text-sm text-gray-600 flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              {user.email}
                            </p>
                          </div>
                        </div>

                        <motion.div
                          className="flex items-center gap-1"
                          whileHover={{ scale: 1.1 }}
                        >
                          {user.active ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-500" />
                          )}
                        </motion.div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Badge className={`${getRoleColor(user.role)} shadow-sm`}>
                            {user.role === 'superadmin' ? 'Super Admin' : 'Administrador'}
                          </Badge>
                          <Badge variant={user.active ? "default" : "secondary"} className="text-xs">
                            {user.active ? 'Activo' : 'Inactivo'}
                          </Badge>
                        </div>

                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <Calendar className="h-3 w-3" />
                          <span>Creado: {new Date(user.created_at).toLocaleDateString('es-ES')}</span>
                        </div>

                        {user.last_login && (
                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <Activity className="h-3 w-3" />
                            <span>Último acceso: {new Date(user.last_login).toLocaleDateString('es-ES')}</span>
                          </div>
                        )}
                      </div>

                      {/* Actions */}
                      {canManageUser(user) && (
                        <div className="flex gap-2 mt-4 pt-4 border-t border-gray-200">
                          <motion.div className="flex-1" whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => openEditDialog(user)}
                              className="w-full hover:bg-blue-50 hover:border-blue-300"
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              Editar
                            </Button>
                          </motion.div>

                          {!isCurrentUser && (
                            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => openDeleteDialog(user)}
                                className="text-red-600 hover:bg-red-50 hover:border-red-300"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </motion.div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </AnimatePresence>
        </div>
      </motion.div>

      {filteredUsers.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No se encontraron usuarios
            </h3>
            <p className="text-gray-600">
              No hay usuarios que coincidan con los filtros aplicados.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Editar Usuario</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              />
              {formData.email !== selectedUser?.email && (
                <Alert>
                  <AlertDescription className="text-sm text-amber-700">
                    ⚠️ Cambiar el email actualizará las credenciales de login del usuario.
                  </AlertDescription>
                </Alert>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-name">Nombre Completo</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Nombre del usuario"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-role">Rol</Label>
              <Select value={formData.role} onValueChange={(value: any) => setFormData(prev => ({ ...prev, role: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {currentUser?.role === 'superadmin' && (
                    <SelectItem value="superadmin">Super Administrador</SelectItem>
                  )}
                  <SelectItem value="admin">Administrador</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-password">Nueva Contraseña (opcional)</Label>
              <Input
                id="edit-password"
                type="password"
                value={formData.password}
                onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                placeholder="Dejar vacío para mantener actual"
              />
            </div>
            
            {formData.password && (
              <div className="space-y-2">
                <Label htmlFor="edit-confirmPassword">Confirmar Nueva Contraseña</Label>
                <Input
                  id="edit-confirmPassword"
                  type="password"
                  value={formData.confirmPassword}
                  onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  placeholder="Repetir nueva contraseña"
                />
              </div>
            )}
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="edit-active"
                checked={formData.active}
                onChange={(e) => setFormData(prev => ({ ...prev, active: e.target.checked }))}
                className="rounded border-gray-300"
              />
              <Label htmlFor="edit-active">Usuario activo</Label>
            </div>
            
            <div className="flex gap-2 pt-4">
              <Button onClick={updateUser} className="flex-1" disabled={loading}>
                {loading ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : null}
                Actualizar Usuario
              </Button>
              <Button variant="outline" onClick={() => {
                setIsEditDialogOpen(false);
                setSelectedUser(null);
                resetForm();
              }}>
                Cancelar
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Confirmar Eliminación
            </DialogTitle>
            <DialogDescription>
              Esta acción no se puede deshacer. El usuario será eliminado permanentemente del sistema.
            </DialogDescription>
          </DialogHeader>

          {userToDelete && (
            <div className="space-y-4">
              <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-red-100 rounded-lg">
                    <Users className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-red-900">{userToDelete.name}</p>
                    <p className="text-sm text-red-700">{userToDelete.email}</p>
                    <p className="text-xs text-red-600">
                      {userToDelete.role === 'superadmin' ? 'Super Administrador' : 'Administrador'}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex gap-2 pt-4">
                <Button
                  onClick={deleteUser}
                  variant="destructive"
                  className="flex-1"
                  disabled={loading}
                >
                  {loading ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : <Trash2 className="h-4 w-4 mr-2" />}
                  Eliminar Usuario
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsDeleteDialogOpen(false);
                    setUserToDelete(null);
                  }}
                  disabled={loading}
                >
                  Cancelar
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export { SimpleUserManagement };
export default SimpleUserManagement;
