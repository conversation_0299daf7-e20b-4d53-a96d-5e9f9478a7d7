import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Bell, AlertTriangle, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface AccessRequestAlertProps {
  count: number;
  priorityCount: number;
  onView: () => void;
  onDismiss: () => void;
}

export const AccessRequestAlert = ({ count, priorityCount, onView, onDismiss }: AccessRequestAlertProps) => {
  const [isVisible, setIsVisible] = useState(true);
  const [pulseEffect, setPulseEffect] = useState(true);
  
  // Efecto de parpadeo para llamar la atención
  useEffect(() => {
    const interval = setInterval(() => {
      setPulseEffect(prev => !prev);
    }, 1500);
    
    return () => clearInterval(interval);
  }, []);
  
  // Reproducir sonido de alerta para prioridad alta
  useEffect(() => {
    if (priorityCount > 0) {
      const audio = new Audio('/notification-sound.mp3');
      audio.play().catch(e => console.error('Error al reproducir sonido:', e));
    }
  }, [priorityCount]);

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => {
      onDismiss();
    }, 300); // Esperar a que termine la animación
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.3 }}
          className="fixed top-4 right-4 z-50 max-w-md"
        >
          <div className={`rounded-lg shadow-lg ${priorityCount > 0 ? 'bg-red-50 border-red-300' : 'bg-blue-50 border-blue-300'} border-2 p-4`}>
            <div className="flex items-start">
              <div className={`p-2 rounded-full ${priorityCount > 0 ? 'bg-red-100' : 'bg-blue-100'} mr-4`}>
                {priorityCount > 0 ? (
                  <AlertTriangle className={`h-6 w-6 ${pulseEffect ? 'text-red-600' : 'text-red-500'} transition-colors duration-700`} />
                ) : (
                  <Bell className={`h-6 w-6 ${pulseEffect ? 'text-blue-600' : 'text-blue-500'} transition-colors duration-700`} />
                )}
              </div>
              
              <div className="flex-1">
                <h3 className={`font-bold text-lg ${priorityCount > 0 ? 'text-red-800' : 'text-blue-800'}`}>
                  {priorityCount > 0 
                    ? '¡Solicitudes prioritarias!' 
                    : 'Nuevas solicitudes de acceso'}
                </h3>
                
                <p className={`text-sm mt-1 ${priorityCount > 0 ? 'text-red-700' : 'text-blue-700'}`}>
                  {count === 1 
                    ? 'Hay 1 nueva solicitud de acceso pendiente'
                    : `Hay ${count} nuevas solicitudes de acceso pendientes`}
                  {priorityCount > 0 && (
                    <>
                      , incluyendo <Badge variant="destructive" className="ml-1 animate-pulse">
                        {priorityCount} {priorityCount === 1 ? 'prioritaria' : 'prioritarias'}
                      </Badge>
                    </>
                  )}
                </p>
                
                <div className="mt-3 flex space-x-2">
                  <Button 
                    onClick={onView}
                    variant={priorityCount > 0 ? "destructive" : "default"}
                    className="flex-1"
                    size="sm"
                  >
                    Revisar ahora
                  </Button>
                  <Button 
                    onClick={handleDismiss}
                    variant="outline"
                    size="icon"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
