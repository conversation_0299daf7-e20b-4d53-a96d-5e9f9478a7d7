import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  Laptop, 
  Monitor, 
  Phone, 
  Headphones, 
  Keyboard, 
  Mouse, 
  Package, 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Trash2, 
  QrCode,
  MapPin,
  User,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  Upload,
  Save,
  X
} from 'lucide-react';

interface AssetItem {
  id: string;
  collectionid: string;
  tipo: string;
  serial?: string;
  modelo?: string;
  estado: string;
  notas?: string;
}

interface AdvancedAssetManagerProps {
  projectId: string;
}

export const AdvancedAssetManager: React.FC<AdvancedAssetManagerProps> = ({ projectId }) => {
  const [assets, setAssets] = useState<AssetItem[]>([]);
  const [filteredAssets, setFilteredAssets] = useState<AssetItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  const [showNewAssetForm, setShowNewAssetForm] = useState(false);
  const [editingAsset, setEditingAsset] = useState<AssetItem | null>(null);
  const [newAssetData, setNewAssetData] = useState({
    collectionid: 'col-1',
    tipo: '',
    serial: '',
    modelo: '',
    estado: 'disponible',
    notas: ''
  });
  const { toast } = useToast();

  // Iconos para cada tipo de activo
  const getAssetIcon = (tipo: string) => {
    const icons = {
      laptop: Laptop,
      monitor: Monitor,
      telefono: Phone,
      cascos: Headphones,
      teclado: Keyboard,
      raton: Mouse,
      otros: Package
    };
    const IconComponent = icons[tipo as keyof typeof icons] || Package;
    return <IconComponent className="h-5 w-5" />;
  };

  // Colores para estados
  const getStatusColor = (estado: string) => {
    const colors = {
      disponible: 'bg-green-100 text-green-800 border-green-200',
      asignado: 'bg-blue-100 text-blue-800 border-blue-200',
      mantenimiento: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      baja: 'bg-red-100 text-red-800 border-red-200'
    };
    return colors[estado as keyof typeof colors] || colors.disponible;
  };

  // Obtener datos de activos desde Supabase
  const fetchAssets = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('asset_items')
        .select('*')
        .order('id', { ascending: false });

      if (error) {
        throw error;
      }

      setAssets(data || []);
      toast({
        title: "Activos cargados",
        description: `Se cargaron ${data?.length || 0} activos desde la base de datos`,
      });
    } catch (error) {
      console.error('Error fetching assets:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar los activos",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Crear nuevo activo
  const createAsset = async () => {
    if (!newAssetData.tipo || !newAssetData.serial) {
      toast({
        title: "Error",
        description: "Por favor completa al menos el tipo y número de serie",
        variant: "destructive",
      });
      return;
    }

    try {
      const { data, error } = await supabase
        .from('asset_items')
        .insert([{
          collectionid: newAssetData.collectionid,
          tipo: newAssetData.tipo,
          serial: newAssetData.serial,
          modelo: newAssetData.modelo || null,
          estado: newAssetData.estado,
          notas: newAssetData.notas || null
        }])
        .select()
        .single();

      if (error) throw error;

      setAssets(prev => [data, ...prev]);
      setNewAssetData({
        collectionid: 'col-1',
        tipo: '',
        serial: '',
        modelo: '',
        estado: 'disponible',
        notas: ''
      });
      setShowNewAssetForm(false);

      toast({
        title: "Activo creado",
        description: `Activo ${data.serial} creado exitosamente`,
      });
    } catch (error) {
      console.error('Error creating asset:', error);
      toast({
        title: "Error",
        description: "No se pudo crear el activo",
        variant: "destructive",
      });
    }
  };

  // Actualizar activo
  const updateAsset = async (assetId: string, updates: Partial<AssetItem>) => {
    try {
      const { data, error } = await supabase
        .from('asset_items')
        .update(updates)
        .eq('id', assetId)
        .select()
        .single();

      if (error) throw error;

      setAssets(prev => prev.map(asset => 
        asset.id === assetId ? { ...asset, ...data } : asset
      ));

      toast({
        title: "Activo actualizado",
        description: `Activo ${data.serial} actualizado exitosamente`,
      });
    } catch (error) {
      console.error('Error updating asset:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar el activo",
        variant: "destructive",
      });
    }
  };

  // Eliminar activo
  const deleteAsset = async (assetId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este activo?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('asset_items')
        .delete()
        .eq('id', assetId);

      if (error) throw error;

      setAssets(prev => prev.filter(asset => asset.id !== assetId));

      toast({
        title: "Activo eliminado",
        description: "Activo eliminado exitosamente",
      });
    } catch (error) {
      console.error('Error deleting asset:', error);
      toast({
        title: "Error",
        description: "No se pudo eliminar el activo",
        variant: "destructive",
      });
    }
  };

  // Cargar activos al montar el componente
  useEffect(() => {
    fetchAssets();
  }, []);

  // Filtrar activos
  useEffect(() => {
    let filtered = assets;

    if (searchQuery) {
      filtered = filtered.filter(asset =>
        asset.serial?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        asset.modelo?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        asset.notas?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (typeFilter !== 'all') {
      filtered = filtered.filter(asset => asset.tipo === typeFilter);
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(asset => asset.estado === statusFilter);
    }

    setFilteredAssets(filtered);
  }, [assets, searchQuery, typeFilter, statusFilter]);

  // Estadísticas
  const stats = {
    total: assets.length,
    disponibles: assets.filter(a => a.estado === 'disponible').length,
    asignados: assets.filter(a => a.estado === 'asignado').length,
    mantenimiento: assets.filter(a => a.estado === 'mantenimiento').length,
    laptops: assets.filter(a => a.tipo === 'laptop').length,
    monitores: assets.filter(a => a.tipo === 'monitor').length,
    telefonos: assets.filter(a => a.tipo === 'telefono').length
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestión Avanzada de Activos</h2>
          <p className="text-gray-600">Control completo del inventario IT</p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Importar
          </Button>
          <Button size="sm" className="bg-blue-600 hover:bg-blue-700" onClick={() => setShowNewAssetForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nuevo Activo
          </Button>
        </div>
      </div>

      {/* Estadísticas rápidas */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        <Card className="border-l-4 border-blue-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total</p>
                <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
              </div>
              <Package className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-green-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Disponibles</p>
                <p className="text-2xl font-bold text-green-600">{stats.disponibles}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-orange-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Asignados</p>
                <p className="text-2xl font-bold text-orange-600">{stats.asignados}</p>
              </div>
              <User className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-yellow-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Mantenimiento</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.mantenimiento}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-purple-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Laptops</p>
                <p className="text-2xl font-bold text-purple-600">{stats.laptops}</p>
              </div>
              <Laptop className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-cyan-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Monitores</p>
                <p className="text-2xl font-bold text-cyan-600">{stats.monitores}</p>
              </div>
              <Monitor className="h-8 w-8 text-cyan-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-indigo-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Teléfonos</p>
                <p className="text-2xl font-bold text-indigo-600">{stats.telefonos}</p>
              </div>
              <Phone className="h-8 w-8 text-indigo-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Buscar por serial, modelo o notas..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Tipo de activo" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos los tipos</SelectItem>
            <SelectItem value="laptop">Laptops</SelectItem>
            <SelectItem value="monitor">Monitores</SelectItem>
            <SelectItem value="telefono">Teléfonos</SelectItem>
            <SelectItem value="cascos">Cascos</SelectItem>
            <SelectItem value="teclado">Teclados</SelectItem>
            <SelectItem value="raton">Ratones</SelectItem>
            <SelectItem value="otros">Otros</SelectItem>
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Estado" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos los estados</SelectItem>
            <SelectItem value="disponible">Disponible</SelectItem>
            <SelectItem value="asignado">Asignado</SelectItem>
            <SelectItem value="mantenimiento">Mantenimiento</SelectItem>
            <SelectItem value="baja">Baja</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Lista de activos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {filteredAssets.map((asset) => (
            <motion.div
              key={asset.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="hover:shadow-lg transition-shadow border-l-4 border-gray-300 hover:border-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        {getAssetIcon(asset.tipo)}
                      </div>
                      <div>
                        <CardTitle className="text-lg">{asset.modelo || 'Sin modelo'}</CardTitle>
                        <p className="text-sm text-gray-600">{asset.serial || 'Sin serial'}</p>
                      </div>
                    </div>
                    <Badge className={getStatusColor(asset.estado)}>
                      {asset.estado.charAt(0).toUpperCase() + asset.estado.slice(1)}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <MapPin className="h-4 w-4" />
                      {asset.notas || 'Sin notas'}
                    </div>
                    
                    <div className="flex gap-2 pt-3">
                      <Button variant="outline" size="sm" onClick={() => setEditingAsset(asset)}>
                        <Edit className="h-4 w-4 mr-1" />
                        Editar
                      </Button>
                      <Button variant="outline" size="sm">
                        <QrCode className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700" onClick={() => deleteAsset(asset.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {filteredAssets.length === 0 && (
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron activos</h3>
          <p className="text-gray-600">
            {searchQuery || typeFilter !== 'all' || statusFilter !== 'all'
              ? 'Intenta ajustar los filtros de búsqueda.'
              : 'Comienza agregando tu primer activo al inventario.'}
          </p>
        </div>
      )}

      {/* Formulario para nuevo activo */}
      {showNewAssetForm && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Nuevo Activo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Input
                placeholder="Tipo de activo"
                value={newAssetData.tipo}
                onChange={(e) => setNewAssetData(prev => ({ ...prev, tipo: e.target.value }))}
              />
              <Input
                placeholder="Número de serie"
                value={newAssetData.serial}
                onChange={(e) => setNewAssetData(prev => ({ ...prev, serial: e.target.value }))}
              />
              <Input
                placeholder="Modelo"
                value={newAssetData.modelo}
                onChange={(e) => setNewAssetData(prev => ({ ...prev, modelo: e.target.value }))}
              />
              <Select value={newAssetData.estado} onValueChange={(value) => setNewAssetData(prev => ({ ...prev, estado: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="disponible">Disponible</SelectItem>
                  <SelectItem value="asignado">Asignado</SelectItem>
                  <SelectItem value="mantenimiento">Mantenimiento</SelectItem>
                  <SelectItem value="baja">Baja</SelectItem>
                </SelectContent>
              </Select>
              <Input
                placeholder="Notas"
                value={newAssetData.notas}
                onChange={(e) => setNewAssetData(prev => ({ ...prev, notas: e.target.value }))}
              />
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" size="sm" onClick={() => setShowNewAssetForm(false)}>
                Cancelar
              </Button>
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700" onClick={createAsset}>
                Crear Activo
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Formulario para editar activo */}
      {editingAsset && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Editar Activo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Input
                placeholder="Tipo de activo"
                value={editingAsset.tipo}
                onChange={(e) => setEditingAsset(prev => prev ? ({ ...prev, tipo: e.target.value }) : null)}
              />
              <Input
                placeholder="Número de serie"
                value={editingAsset.serial || ''}
                onChange={(e) => setEditingAsset(prev => prev ? ({ ...prev, serial: e.target.value }) : null)}
              />
              <Input
                placeholder="Modelo"
                value={editingAsset.modelo || ''}
                onChange={(e) => setEditingAsset(prev => prev ? ({ ...prev, modelo: e.target.value }) : null)}
              />
              <Select value={editingAsset.estado} onValueChange={(value) => setEditingAsset(prev => prev ? ({ ...prev, estado: value }) : null)}>
                <SelectTrigger>
                  <SelectValue placeholder="Estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="disponible">Disponible</SelectItem>
                  <SelectItem value="asignado">Asignado</SelectItem>
                  <SelectItem value="mantenimiento">Mantenimiento</SelectItem>
                  <SelectItem value="baja">Baja</SelectItem>
                </SelectContent>
              </Select>
              <Input
                placeholder="Notas"
                value={editingAsset.notas || ''}
                onChange={(e) => setEditingAsset(prev => prev ? ({ ...prev, notas: e.target.value }) : null)}
              />
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" size="sm" onClick={() => setEditingAsset(null)}>
                Cancelar
              </Button>
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700" onClick={() => editingAsset && updateAsset(editingAsset.id, editingAsset)}>
                Guardar Cambios
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
