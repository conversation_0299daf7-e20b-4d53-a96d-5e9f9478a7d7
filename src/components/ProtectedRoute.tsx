import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

type ProtectedRouteProps = {
  children: React.ReactNode;
  roles?: string[];
  redirectTo?: string;
};

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  roles = [],
  redirectTo = '/admin-login',
}) => {
  const { user, loading, hasRole } = useAuth();
  const location = useLocation();

  console.log('🛡️ ProtectedRoute check:', { 
    user: user ? { id: user.id, email: user.email, role: user.role } : null, 
    loading, 
    pathname: location.pathname,
    roles 
  });

  if (loading) {
    console.log('⏳ ProtectedRoute: Still loading...');
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Verificando autenticación...</p>
        </div>
      </div>
    );
  }

  // Si no hay usuario autenticado, redirigir al login
  if (!user) {
    console.log('❌ ProtectedRoute: No user found, redirecting to login');
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // Si el usuario no tiene el rol requerido, redirigir al dashboard
  if (roles.length > 0 && !hasRole(roles)) {
    console.log('🚫 ProtectedRoute: User lacks required roles, redirecting to dashboard');
    return <Navigate to="/admin-dashboard" state={{ from: location }} replace />;
  }

  console.log('✅ ProtectedRoute: Access granted');
  // Si el usuario está autenticado y tiene los roles necesarios, renderizar los hijos
  return <>{children}</>;
};

export default ProtectedRoute;
