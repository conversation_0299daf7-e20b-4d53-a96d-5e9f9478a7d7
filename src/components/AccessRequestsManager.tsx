import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { 
  User, 
  Calendar, 
  Globe, 
  Settings,
  CheckCircle2,
  XCircle,
  Clock,
  AlertTriangle,
  RefreshCw,
  Trash2,
  Bell,
  BellRing
} from 'lucide-react';

interface AccessRequest {
  id: string;
  empleado_nombre: string;
  empleado_cdsid: string;
  region: string;
  plataformas_faltantes: string[];
  descripcion: string;
  prioridad: string;
  estado: string;
  created_at: string;
  updated_at: string;
}

export const AccessRequestsManager = () => {
  const [filterStatus, setFilterStatus] = useState('all');
  const [lastNotificationTime, setLastNotificationTime] = useState<Date | null>(null);
  const [newRequestsCount, setNewRequestsCount] = useState(0);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Obtener solicitudes de acceso con refresco automático
  const { data: requests = [], isLoading } = useQuery({
    queryKey: ['access-requests'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('notificaciones_acceso')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as AccessRequest[];
    },
    refetchInterval: 30000, // Refresca cada 30 segundos
    refetchOnWindowFocus: true,
  });

  // Efecto para detectar nuevas solicitudes
  useEffect(() => {
    if (!requests || requests.length === 0) return;

    const now = new Date();
    const recentRequests = requests.filter(request => {
      const requestTime = new Date(request.created_at);
      return requestTime > (lastNotificationTime || new Date(now.getTime() - 60000)); // Últimos 60 segundos
    });

    if (recentRequests.length > 0 && lastNotificationTime) {
      setNewRequestsCount(prev => prev + recentRequests.length);
      
      // Mostrar notificación toast
      toast({
        title: "🔔 Nueva Solicitud de Acceso",
        description: `${recentRequests.length} nueva${recentRequests.length > 1 ? 's' : ''} solicitud${recentRequests.length > 1 ? 'es' : ''} recibida${recentRequests.length > 1 ? 's' : ''}`,
        duration: 5000,
      });

      // Si hay solicitudes de alta prioridad, mostrar notificación especial
      const highPriorityRequests = recentRequests.filter(r => r.prioridad === 'alta');
      if (highPriorityRequests.length > 0) {
        toast({
          title: "⚠️ Solicitud de Alta Prioridad",
          description: `${highPriorityRequests.length} solicitud${highPriorityRequests.length > 1 ? 'es' : ''} de alta prioridad requiere${highPriorityRequests.length > 1 ? 'n' : ''} atención inmediata`,
          variant: "destructive",
          duration: 8000,
        });
      }
    }

    setLastNotificationTime(now);
  }, [requests, lastNotificationTime, toast]);

  // Configurar suscripción en tiempo real
  useEffect(() => {
    const channel = supabase
      .channel('access_requests_channel')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notificaciones_acceso'
        },
        (payload) => {
          console.log('Nueva solicitud de acceso:', payload);
          
          // Refrescar datos
          queryClient.invalidateQueries({ queryKey: ['access-requests'] });
          
          // Mostrar notificación inmediata
          toast({
            title: "🆕 Nueva Solicitud Recibida",
            description: `Solicitud de ${payload.new.empleado_nombre} - Prioridad: ${payload.new.prioridad}`,
            duration: 6000,
          });

          setNewRequestsCount(prev => prev + 1);
        }
      )
      .subscribe();

    return () => {
      channel.unsubscribe();
    };
  }, [queryClient, toast]);

  // Función para marcar como vistas las nuevas solicitudes
  const markAsViewed = () => {
    setNewRequestsCount(0);
  };

  // Actualizar estado de solicitud
  const updateStatusMutation = useMutation({
    mutationFn: async ({ id, estado }: { id: string; estado: string }) => {
      const { error } = await supabase
        .from('notificaciones_acceso')
        .update({ estado, updated_at: new Date().toISOString() })
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['access-requests'] });
      toast({
        title: "Estado actualizado",
        description: "El estado de la solicitud ha sido actualizado exitosamente",
      });
    },
    onError: (error) => {
      console.error('Error updating status:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar el estado de la solicitud",
        variant: "destructive",
      });
    }
  });

  // Eliminar solicitud
  const deleteRequestMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('notificaciones_acceso')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['access-requests'] });
      toast({
        title: "Solicitud eliminada",
        description: "La solicitud ha sido eliminada exitosamente",
      });
    },
    onError: (error) => {
      console.error('Error deleting request:', error);
      toast({
        title: "Error",
        description: "No se pudo eliminar la solicitud",
        variant: "destructive",
      });
    }
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'alta': return 'bg-red-100 text-red-800 border-red-200';
      case 'media': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'baja': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pendiente': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'en_proceso': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completado': return 'bg-green-100 text-green-800 border-green-200';
      case 'rechazado': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pendiente': return <Clock className="w-4 h-4" />;
      case 'en_proceso': return <Settings className="w-4 h-4" />;
      case 'completado': return <CheckCircle2 className="w-4 h-4" />;
      case 'rechazado': return <XCircle className="w-4 h-4" />;
      default: return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const filteredRequests = requests.filter(request => {
    if (filterStatus === 'all') return true;
    return request.estado === filterStatus;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Calcular estadísticas
  const stats = {
    total: requests.length,
    pendientes: requests.filter(r => r.estado === 'pendiente').length,
    enProceso: requests.filter(r => r.estado === 'en_proceso').length,
    completadas: requests.filter(r => r.estado === 'completado').length,
    altaPrioridad: requests.filter(r => r.prioridad === 'alta').length
  };

  if (isLoading) {
    return (
      <Card className="shadow-xl border-2 border-gray-200">
        <CardContent className="p-12">
          <div className="text-center">
            <RefreshCw className="h-16 w-16 mx-auto text-primary animate-spin mb-4" />
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Cargando Solicitudes</h3>
            <p className="text-gray-600 text-lg">Sincronizando con la base de datos...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Notificación de nuevas solicitudes */}
      {newRequestsCount > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <BellRing className="h-6 w-6 text-orange-600 animate-pulse" />
                <div>
                  <h4 className="font-bold text-orange-800">
                    {newRequestsCount} nueva{newRequestsCount > 1 ? 's' : ''} solicitud{newRequestsCount > 1 ? 'es' : ''} recibida{newRequestsCount > 1 ? 's' : ''}
                  </h4>
                  <p className="text-sm text-orange-700">
                    Revise las solicitudes pendientes y tome las acciones necesarias
                  </p>
                </div>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={markAsViewed}
                className="border-orange-300 hover:bg-orange-100"
              >
                Marcar como visto
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Estadísticas rápidas */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-600">Total</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.pendientes}</div>
            <div className="text-sm text-gray-600">Pendientes</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.enProceso}</div>
            <div className="text-sm text-gray-600">En Proceso</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{stats.completadas}</div>
            <div className="text-sm text-gray-600">Completadas</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{stats.altaPrioridad}</div>
            <div className="text-sm text-gray-600">Alta Prioridad</div>
          </CardContent>
        </Card>
      </div>

      {/* Header y Filtros */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <div>
            <h2 className="text-3xl font-bold text-gray-900">Solicitudes de Acceso</h2>
            <p className="text-gray-600 mt-2">Gestiona las solicitudes de acceso a plataformas corporativas</p>
          </div>
          {newRequestsCount > 0 && (
            <Badge className="bg-red-500 text-white animate-bounce">
              <Bell className="h-3 w-3 mr-1" />
              {newRequestsCount} nuevas
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-4">
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filtrar por estado" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas las solicitudes</SelectItem>
              <SelectItem value="pendiente">Pendientes</SelectItem>
              <SelectItem value="en_proceso">En Proceso</SelectItem>
              <SelectItem value="completado">Completadas</SelectItem>
              <SelectItem value="rechazado">Rechazadas</SelectItem>
            </SelectContent>
          </Select>
          <Badge variant="outline" className="px-4 py-2 text-sm font-bold">
            {filteredRequests.length} solicitudes
          </Badge>
        </div>
      </div>

      {/* Lista de Solicitudes */}
      <div className="space-y-4">
        {filteredRequests.length === 0 ? (
          <Card className="text-center py-20">
            <CardContent>
              <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No hay solicitudes</h3>
              <p className="text-gray-500">
                {filterStatus === 'all' 
                  ? 'No se encontraron solicitudes de acceso.'
                  : `No hay solicitudes con estado "${filterStatus}".`
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredRequests.map((request) => (
            <Card key={request.id} className="hover:shadow-lg transition-shadow border-2">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-3">
                      <Badge className={getPriorityColor(request.prioridad)}>
                        {request.prioridad.toUpperCase()}
                      </Badge>
                      <Badge className={getStatusColor(request.estado)}>
                        {getStatusIcon(request.estado)}
                        <span className="ml-1 capitalize">{request.estado.replace('_', ' ')}</span>
                      </Badge>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Select
                      value={request.estado}
                      onValueChange={(value) => updateStatusMutation.mutate({ id: request.id, estado: value })}
                    >
                      <SelectTrigger className="w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pendiente">Pendiente</SelectItem>
                        <SelectItem value="en_proceso">En Proceso</SelectItem>
                        <SelectItem value="completado">Completado</SelectItem>
                        <SelectItem value="rechazado">Rechazado</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteRequestMutation.mutate(request.id)}
                      className="text-red-500 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg border">
                    <User className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="font-semibold text-gray-900">{request.empleado_nombre}</p>
                      <p className="text-sm text-gray-600">CDSID: {request.empleado_cdsid}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg border">
                    <Globe className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="font-semibold text-gray-900">{request.region}</p>
                      <p className="text-sm text-gray-600">Región</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3 p-4 bg-purple-50 rounded-lg border">
                    <Calendar className="h-5 w-5 text-purple-600" />
                    <div>
                      <p className="text-sm font-semibold text-gray-900">{formatDate(request.created_at)}</p>
                      <p className="text-sm text-gray-600">Fecha de Solicitud</p>
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Settings className="h-5 w-5 text-orange-600" />
                    <p className="font-semibold text-gray-900">Plataformas Solicitadas:</p>
                    <Badge variant="secondary" className="bg-orange-100 text-orange-700 border border-orange-200">
                      {request.plataformas_faltantes.length} plataformas
                    </Badge>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {request.plataformas_faltantes.map((platform) => (
                      <Badge 
                        key={platform} 
                        variant="outline" 
                        className="text-sm bg-white border-gray-300 text-gray-700 p-2 justify-center"
                      >
                        {platform}
                      </Badge>
                    ))}
                  </div>
                </div>

                {request.descripcion && (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="h-5 w-5 text-blue-600 mt-1" />
                      <div>
                        <p className="font-semibold text-blue-800 mb-1">Descripción:</p>
                        <p className="text-sm text-blue-700 leading-relaxed">{request.descripcion}</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};
