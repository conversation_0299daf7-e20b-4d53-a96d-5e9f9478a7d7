
import { Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function ModeToggle() {
  return (
    <Button 
      variant="outline" 
      size="sm" 
      className="h-10 w-10 p-0 bg-white border-gray-200 hover:bg-gray-50 transition-all duration-300"
      disabled
    >
      <Monitor className="h-5 w-5 text-gray-400" />
      <span className="sr-only">Modo claro activado</span>
    </Button>
  );
}
