import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Plus, 
  Search, 
  Filter,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  Hash,
  Headphones
} from 'lucide-react';

interface Ticket {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: 'access' | 'hardware' | 'software' | 'network' | 'general';
  creator_email: string;
  creator_id: string;
  affected_cdsid?: string;
  assigned_to?: string;
  created_at: string;
  updated_at: string;
  closed_at?: string;
  resolution_time?: number;
}

export const TicketingSystem = () => {
  const [showNewTicketForm, setShowNewTicketForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [newTicket, setNewTicket] = useState({
    title: '',
    description: '',
    category: 'general',
    priority: 'medium',
    creator_email: '',
    affected_cdsid: ''
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Obtener tickets
  const { data: tickets = [], isLoading } = useQuery({
    queryKey: ['tickets'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tickets')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as Ticket[];
    }
  });

  // Crear nuevo ticket
  const createTicketMutation = useMutation({
    mutationFn: async (ticket: typeof newTicket) => {
      // Map the form data to match database schema
      const ticketData = {
        title: ticket.title,
        description: ticket.description,
        category: ticket.category,
        priority: ticket.priority,
        creator_email: ticket.creator_email,
        creator_id: ticket.creator_email, // Using email as ID for now
        affected_cdsid: ticket.affected_cdsid,
        status: 'pending' as const
      };

      const { data, error } = await supabase
        .from('tickets')
        .insert([ticketData])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tickets'] });
      setShowNewTicketForm(false);
      setNewTicket({
        title: '',
        description: '',
        category: 'general',
        priority: 'medium',
        creator_email: '',
        affected_cdsid: ''
      });
      toast({
        title: "Ticket creado exitosamente",
        description: "Tu solicitud ha sido registrada en el sistema",
      });
    },
    onError: (error) => {
      console.error('Error creating ticket:', error);
      toast({
        title: "Error al crear ticket",
        description: "No se pudo crear el ticket. Inténtalo de nuevo.",
        variant: "destructive",
      });
    }
  });

  const handleSubmitTicket = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newTicket.title || !newTicket.description || !newTicket.creator_email) {
      toast({
        title: "Campos requeridos",
        description: "Por favor completa todos los campos obligatorios",
        variant: "destructive",
      });
      return;
    }
    createTicketMutation.mutate(newTicket);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'resolved': return 'bg-green-100 text-green-800 border-green-200';
      case 'closed': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <AlertCircle className="w-4 h-4" />;
      case 'in_progress': return <Clock className="w-4 h-4" />;
      case 'resolved': return <CheckCircle className="w-4 h-4" />;
      case 'closed': return <XCircle className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  const filteredTickets = tickets.filter(ticket => {
    if (!ticket) return false;
    return (
      (ticket.title && ticket.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (ticket.id && ticket.id.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (ticket.creator_email && ticket.creator_email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (ticket.affected_cdsid && ticket.affected_cdsid.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  });

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Sistema de Tickets</h2>
          <p className="text-gray-600 mt-2">Gestiona incidencias y solicitudes de soporte técnico</p>
        </div>
        <Button 
          onClick={() => setShowNewTicketForm(true)}
          className="bg-green-600 hover:bg-green-700 text-white font-semibold px-6 py-3"
        >
          <Plus className="w-5 h-5 mr-2" />
          Nuevo Ticket
        </Button>
      </div>

      {/* Búsqueda y filtros */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Buscar tickets por título, ID, email o CDSID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline">
          <Filter className="w-4 h-4 mr-2" />
          Filtros
        </Button>
      </div>

      {/* Formulario de nuevo ticket */}
      {showNewTicketForm && (
        <Card className="border-2 border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800">
              <Plus className="w-5 h-5" />
              Crear Nuevo Ticket
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmitTicket} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Título del Ticket *
                  </label>
                  <Input
                    value={newTicket.title}
                    onChange={(e) => setNewTicket({...newTicket, title: e.target.value})}
                    placeholder="Describe brevemente el problema"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Categoría
                  </label>
                  <select
                    value={newTicket.category}
                    onChange={(e) => setNewTicket({...newTicket, category: e.target.value})}
                    className="w-full h-10 px-3 border border-gray-300 rounded-md"
                  >
                    <option value="general">General</option>
                    <option value="access">Accesos</option>
                    <option value="hardware">Hardware</option>
                    <option value="software">Software</option>
                    <option value="network">Red</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email *
                  </label>
                  <Input
                    type="email"
                    value={newTicket.creator_email}
                    onChange={(e) => setNewTicket({...newTicket, creator_email: e.target.value})}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    CDSID Afectado
                  </label>
                  <Input
                    value={newTicket.affected_cdsid}
                    onChange={(e) => setNewTicket({...newTicket, affected_cdsid: e.target.value})}
                    placeholder="CDSID del usuario afectado"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prioridad
                </label>
                <select
                  value={newTicket.priority}
                  onChange={(e) => setNewTicket({...newTicket, priority: e.target.value})}
                  className="w-full h-10 px-3 border border-gray-300 rounded-md"
                >
                  <option value="low">Baja</option>
                  <option value="medium">Media</option>
                  <option value="high">Alta</option>
                  <option value="critical">Crítica</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Descripción *
                </label>
                <Textarea
                  value={newTicket.description}
                  onChange={(e) => setNewTicket({...newTicket, description: e.target.value})}
                  placeholder="Describe detalladamente el problema o solicitud..."
                  rows={4}
                  required
                />
              </div>

              <div className="flex gap-3">
                <Button 
                  type="submit" 
                  disabled={createTicketMutation.isPending}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {createTicketMutation.isPending ? 'Creando...' : 'Crear Ticket'}
                </Button>
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => setShowNewTicketForm(false)}
                >
                  Cancelar
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Lista de tickets */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="text-gray-500 mt-4">Cargando tickets...</p>
          </div>
        ) : filteredTickets.length === 0 ? (
          <Card className="text-center py-12">
            <CardContent>
              <Headphones className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No hay tickets</h3>
              <p className="text-gray-500">No se encontraron tickets que coincidan con tu búsqueda.</p>
            </CardContent>
          </Card>
        ) : (
          filteredTickets.map((ticket) => (
            <Card key={ticket.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <Badge variant="outline" className="font-mono text-xs">
                        <Hash className="w-3 h-3 mr-1" />
                        {ticket.id.slice(0, 8)}
                      </Badge>
                      <Badge className={getPriorityColor(ticket.priority)}>
                        {ticket.priority.toUpperCase()}
                      </Badge>
                      <Badge className={getStatusColor(ticket.status)}>
                        {getStatusIcon(ticket.status)}
                        <span className="ml-1 capitalize">{ticket.status.replace('_', ' ')}</span>
                      </Badge>
                    </div>
                    
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {ticket.title}
                    </h3>
                    
                    <p className="text-gray-600 mb-4 line-clamp-2">
                      {ticket.description}
                    </p>
                    
                    <div className="flex items-center gap-6 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <User className="w-4 h-4" />
                        {ticket.creator_email}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {new Date(ticket.created_at).toLocaleDateString('es-ES')}
                      </div>
                      <div className="capitalize">
                        Categoría: {ticket.category}
                      </div>
                      {ticket.affected_cdsid && (
                        <div>
                          CDSID: {ticket.affected_cdsid}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};
