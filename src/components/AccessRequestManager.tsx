import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  UserPlus, 
  Search, 
  Filter, 
  Eye, 
  Check, 
  X, 
  Clock, 
  AlertTriangle,
  Mail,
  Building,
  User,
  Calendar,
  MessageSquare,
  RefreshCw
} from 'lucide-react';

interface AccessRequest {
  id: string;
  cdsid: string;
  nombre: string;
  apellidos: string;
  mercado: string;
  accesos_solicitados: string[] | null;
  justificacion: string | null;
  estado: 'solicitada' | 'reclamada' | 'con_acceso_pendiente_verificar' | 'verificado' | 'cerrada' | 'escalada' | null;
  created_at: string | null;
  updated_at: string | null;
}

export const AccessRequestManager: React.FC = () => {
  const [requests, setRequests] = useState<AccessRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<AccessRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedRequest, setSelectedRequest] = useState<AccessRequest | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchRequests();
    
    // Subscribe to real-time updates
    const subscription = supabase
      .channel('access_requests')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'solicitudes_acceso' },
        () => {
          fetchRequests();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  useEffect(() => {
    filterRequests();
  }, [requests, searchTerm, statusFilter]);

  const fetchRequests = async () => {
    try {
      const { data, error } = await supabase
        .from('solicitudes_acceso')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setRequests(data || []);
    } catch (error) {
      console.error('Error fetching access requests:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar las solicitudes de acceso",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const filterRequests = () => {
    let filtered = requests;

    if (searchTerm) {
      filtered = filtered.filter(request =>
        request.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.apellidos.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.cdsid.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.mercado.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.justificacion?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(request => request.estado === statusFilter);
    }

    setFilteredRequests(filtered);
  };

  const updateRequestStatus = async (requestId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('solicitudes_acceso')
        .update({
          estado: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (error) throw error;

      const statusMessages = {
        'reclamada': 'reclamada',
        'con_acceso_pendiente_verificar': 'marcada como con acceso pendiente de verificar',
        'verificado': 'verificada',
        'cerrada': 'cerrada',
        'escalada': 'escalada'
      };

      toast({
        title: "Solicitud Actualizada",
        description: `La solicitud ha sido ${statusMessages[newStatus] || newStatus} exitosamente`,
      });

      fetchRequests();
      setSelectedRequest(null);
    } catch (error) {
      console.error('Error updating request:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar la solicitud",
        variant: "destructive"
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'solicitada':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          <Clock className="h-3 w-3 mr-1" />
          Solicitada
        </Badge>;
      case 'reclamada':
        return <Badge variant="secondary" className="bg-orange-50 text-orange-700 border-orange-200">
          <AlertTriangle className="h-3 w-3 mr-1" />
          Reclamada
        </Badge>;
      case 'con_acceso_pendiente_verificar':
        return <Badge variant="default" className="bg-yellow-50 text-yellow-700 border-yellow-200">
          <Clock className="h-3 w-3 mr-1" />
          Con Acceso - Pendiente Verificar
        </Badge>;
      case 'verificado':
        return <Badge variant="default" className="bg-green-50 text-green-700 border-green-200">
          <Check className="h-3 w-3 mr-1" />
          Verificado
        </Badge>;
      case 'cerrada':
        return <Badge variant="destructive" className="bg-gray-50 text-gray-700 border-gray-200">
          <X className="h-3 w-3 mr-1" />
          Cerrada
        </Badge>;
      case 'escalada':
        return <Badge variant="destructive" className="bg-red-50 text-red-700 border-red-200">
          <AlertTriangle className="h-3 w-3 mr-1" />
          Escalada
        </Badge>;
      default:
        return <Badge variant="outline">
          <Clock className="h-3 w-3 mr-1" />
          {status || 'Sin estado'}
        </Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRequestStats = () => {
    const total = requests.length;
    const pending = requests.filter(r => r.estado === 'pendiente').length;
    const approved = requests.filter(r => r.estado === 'aprobada').length;
    const rejected = requests.filter(r => r.estado === 'rechazada').length;

    return { total, pending, approved, rejected };
  };

  const stats = getRequestStats();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Cargando solicitudes de acceso...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <UserPlus className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Solicitadas</p>
                <p className="text-2xl font-bold text-blue-600">{requests.filter(r => r.estado === 'solicitada').length}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Con Acceso</p>
                <p className="text-2xl font-bold text-yellow-600">{requests.filter(r => r.estado === 'con_acceso_pendiente_verificar').length}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Verificadas</p>
                <p className="text-2xl font-bold text-green-600">{requests.filter(r => r.estado === 'verificado').length}</p>
              </div>
              <Check className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Gestión de Solicitudes de Acceso
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar por nombre, CDSID, región o descripción..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filtrar por estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los estados</SelectItem>
                <SelectItem value="solicitada">Solicitadas</SelectItem>
                <SelectItem value="reclamada">Reclamadas</SelectItem>
                <SelectItem value="con_acceso_pendiente_verificar">Con Acceso - Pendiente Verificar</SelectItem>
                <SelectItem value="verificado">Verificadas</SelectItem>
                <SelectItem value="cerrada">Cerradas</SelectItem>
                <SelectItem value="escalada">Escaladas</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={fetchRequests} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualizar
            </Button>
          </div>

          {/* Requests List */}
          <div className="space-y-4">
            <AnimatePresence>
              {filteredRequests.map((request) => (
                <motion.div
                  key={request.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-gray-900">{request.nombre} {request.apellidos}</h3>
                        {getStatusBadge(request.estado)}
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span>CDSID: {request.cdsid}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          <span>{request.mercado}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>{request.created_at ? formatDate(request.created_at) : 'N/A'}</span>
                        </div>
                      </div>
                      
                      <div className="mt-3">
                        <p className="text-sm text-gray-700">
                          <strong>Justificación:</strong> {request.justificacion || 'Sin justificación'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedRequest(request)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Ver Detalles
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Detalles de Solicitud de Acceso</DialogTitle>
                          </DialogHeader>
                          
                          {selectedRequest && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium text-gray-700">Nombre</label>
                                  <p className="text-sm text-gray-900">{selectedRequest.nombre} {selectedRequest.apellidos}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-700">CDSID</label>
                                  <p className="text-sm text-gray-900">{selectedRequest.cdsid}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-700">Mercado</label>
                                  <p className="text-sm text-gray-900">{selectedRequest.mercado}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-700">Estado</label>
                                  <div className="mt-1">{getStatusBadge(selectedRequest.estado)}</div>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-700">Prioridad</label>
                                  <p className="text-sm text-gray-900">{selectedRequest.prioridad}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-700">Fecha de Solicitud</label>
                                  <p className="text-sm text-gray-900">{selectedRequest.created_at ? formatDate(selectedRequest.created_at) : 'N/A'}</p>
                                </div>
                              </div>
                              
                              <div>
                                <label className="text-sm font-medium text-gray-700">Justificación</label>
                                <p className="text-sm text-gray-900 mt-1 p-3 bg-gray-50 rounded-md">
                                  {selectedRequest.justificacion || 'Sin justificación'}
                                </p>
                              </div>

                              <div>
                                <label className="text-sm font-medium text-gray-700">Accesos Solicitados</label>
                                <div className="mt-1 flex flex-wrap gap-2">
                                  {selectedRequest.accesos_solicitados?.map((acceso, index) => (
                                    <Badge key={index} variant="outline">
                                      {acceso}
                                    </Badge>
                                  )) || <span className="text-gray-500">No hay accesos especificados</span>}
                                </div>
                              </div>

                              <div className="flex flex-wrap gap-2 pt-4">
                                {selectedRequest.estado === 'solicitada' && (
                                  <Button
                                    onClick={() => updateRequestStatus(selectedRequest.id, 'reclamada')}
                                    className="bg-orange-600 hover:bg-orange-700"
                                  >
                                    <AlertTriangle className="h-4 w-4 mr-2" />
                                    Reclamar
                                  </Button>
                                )}

                                {selectedRequest.estado === 'reclamada' && (
                                  <>
                                    <Button
                                      onClick={() => updateRequestStatus(selectedRequest.id, 'con_acceso_pendiente_verificar')}
                                      className="bg-yellow-600 hover:bg-yellow-700"
                                    >
                                      <Clock className="h-4 w-4 mr-2" />
                                      Marcar Con Acceso
                                    </Button>
                                    <Button
                                      onClick={() => updateRequestStatus(selectedRequest.id, 'escalada')}
                                      variant="destructive"
                                    >
                                      <AlertTriangle className="h-4 w-4 mr-2" />
                                      Escalar
                                    </Button>
                                  </>
                                )}

                                {selectedRequest.estado === 'con_acceso_pendiente_verificar' && (
                                  <Button
                                    onClick={() => updateRequestStatus(selectedRequest.id, 'verificado')}
                                    className="bg-green-600 hover:bg-green-700"
                                  >
                                    <Check className="h-4 w-4 mr-2" />
                                    Verificar
                                  </Button>
                                )}

                                {(selectedRequest.estado === 'verificado' || selectedRequest.estado === 'escalada') && (
                                  <Button
                                    onClick={() => updateRequestStatus(selectedRequest.id, 'cerrada')}
                                    variant="outline"
                                  >
                                    <X className="h-4 w-4 mr-2" />
                                    Cerrar
                                  </Button>
                                )}
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>

            {filteredRequests.length === 0 && (
              <div className="text-center py-12">
                <UserPlus className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No se encontraron solicitudes
                </h3>
                <p className="text-gray-600">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'Intenta ajustar los filtros de búsqueda'
                    : 'No hay solicitudes de acceso registradas'
                  }
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AccessRequestManager;
