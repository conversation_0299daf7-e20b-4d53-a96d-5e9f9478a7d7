import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Ticket, 
  Users, 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  TrendingUp, 
  RefreshCw,
  Activity,
  Shield,
  Laptop,
  Monitor,
  Phone
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface StatsData {
  tickets: {
    total: number;
    open: number;
    in_progress: number;
    resolved: number;
    critical: number;
  };
  access_requests: {
    total: number;
    pending: number;
    in_process: number;
    completed: number;
    high_priority: number;
  };
  assets: {
    total: number;
    laptops: number;
    monitors: number;
    phones: number;
    available: number;
  };
}

interface RealTimeStatsProps {
  projectId: string;
}

export const RealTimeStats: React.FC<RealTimeStatsProps> = ({ projectId }) => {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Función para obtener estadísticas reales desde Supabase
  const fetchStats = async () => {
    setIsLoading(true);
    try {
      // Obtener estadísticas de tickets
      const { data: tickets, error: ticketsError } = await supabase
        .from('tickets')
        .select('estado, prioridad');
      
      if (ticketsError) {
        console.error('Error fetching tickets:', ticketsError);
      }

      // Obtener estadísticas de solicitudes de acceso
      const { data: accessRequests, error: accessError } = await supabase
        .from('notificaciones_acceso')
        .select('estado, prioridad');
      
      if (accessError) {
        console.error('Error fetching access requests:', accessError);
      }

      // Obtener estadísticas de activos
      const { data: assets, error: assetsError } = await supabase
        .from('asset_items')
        .select('tipo, estado');
      
      if (assetsError) {
        console.error('Error fetching assets:', assetsError);
      }

      // Procesar estadísticas de tickets
      const ticketStats = {
        total: tickets?.length || 0,
        open: tickets?.filter(t => t.estado === 'abierto').length || 0,
        in_progress: tickets?.filter(t => t.estado === 'en_proceso').length || 0,
        resolved: tickets?.filter(t => t.estado === 'resuelto').length || 0,
        critical: tickets?.filter(t => t.prioridad === 'critica').length || 0
      };

      // Procesar estadísticas de solicitudes de acceso
      const accessStats = {
        total: accessRequests?.length || 0,
        pending: accessRequests?.filter(a => a.estado === 'pendiente').length || 0,
        in_process: accessRequests?.filter(a => a.estado === 'en_proceso').length || 0,
        completed: accessRequests?.filter(a => a.estado === 'completado').length || 0,
        high_priority: accessRequests?.filter(a => a.prioridad === 'alta' || a.prioridad === 'critica').length || 0
      };

      // Procesar estadísticas de activos
      const assetStats = {
        total: assets?.length || 0,
        laptops: assets?.filter(a => a.tipo === 'laptop').length || 0,
        monitors: assets?.filter(a => a.tipo === 'monitor' || a.tipo === 'desktop').length || 0,
        phones: assets?.filter(a => a.tipo === 'smartphone').length || 0,
        available: assets?.filter(a => a.estado === 'disponible').length || 0
      };

      setStats({
        tickets: ticketStats,
        access_requests: accessStats,
        assets: assetStats
      });
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching stats:', error);
      // En caso de error, mostrar estadísticas vacías en lugar de datos mock
      setStats({
        tickets: { total: 0, open: 0, in_progress: 0, resolved: 0, critical: 0 },
        access_requests: { total: 0, pending: 0, in_process: 0, completed: 0, high_priority: 0 },
        assets: { total: 0, laptops: 0, monitors: 0, phones: 0, available: 0 }
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    
    // Actualizar cada 30 segundos
    const interval = setInterval(fetchStats, 30000);
    
    return () => clearInterval(interval);
  }, [projectId]);

  if (!stats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-16 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const statCards = [
    {
      title: "Total Tickets",
      value: stats.tickets.total,
      icon: Ticket,
      color: "blue",
      trend: "****%",
      subtitle: `${stats.tickets.resolved} resueltos`
    },
    {
      title: "Tickets Críticos",
      value: stats.tickets.critical,
      icon: AlertTriangle,
      color: stats.tickets.critical > 0 ? "red" : "green",
      trend: stats.tickets.critical > 0 ? "¡Atención!" : "Todo OK",
      subtitle: "Prioridad alta"
    },
    {
      title: "Solicitudes Pendientes",
      value: stats.access_requests.pending,
      icon: Clock,
      color: stats.access_requests.pending > 0 ? "orange" : "green",
      trend: stats.access_requests.high_priority > 0 ? "Alta prioridad" : "Normal",
      subtitle: `${stats.access_requests.completed} completadas`
    },
    {
      title: "Activos Totales",
      value: stats.assets.total,
      icon: Laptop,
      color: "purple",
      trend: `${stats.assets.available} disponibles`,
      subtitle: "Inventario actualizado"
    },
    {
      title: "Laptops",
      value: stats.assets.laptops,
      icon: Laptop,
      color: "indigo",
      trend: "85% asignadas",
      subtitle: "Equipos principales"
    },
    {
      title: "Monitores",
      value: stats.assets.monitors,
      icon: Monitor,
      color: "cyan",
      trend: "Disponibles",
      subtitle: "Pantallas adicionales"
    },
    {
      title: "Teléfonos",
      value: stats.assets.phones,
      icon: Phone,
      color: "green",
      trend: "Activos",
      subtitle: "Comunicación"
    },
    {
      title: "Eficiencia",
      value: "94%",
      icon: TrendingUp,
      color: "emerald",
      trend: "+5% este mes",
      subtitle: "Resolución promedio"
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: "border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 text-blue-700",
      red: "border-red-500 bg-gradient-to-br from-red-50 to-red-100 text-red-700",
      green: "border-green-500 bg-gradient-to-br from-green-50 to-green-100 text-green-700",
      orange: "border-orange-500 bg-gradient-to-br from-orange-50 to-orange-100 text-orange-700",
      purple: "border-purple-500 bg-gradient-to-br from-purple-50 to-purple-100 text-purple-700",
      indigo: "border-indigo-500 bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-700",
      cyan: "border-cyan-500 bg-gradient-to-br from-cyan-50 to-cyan-100 text-cyan-700",
      emerald: "border-emerald-500 bg-gradient-to-br from-emerald-50 to-emerald-100 text-emerald-700"
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="space-y-6">
      {/* Header con botón de actualización */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Dashboard en Tiempo Real</h2>
          <p className="text-gray-600">
            Última actualización: {lastUpdated.toLocaleTimeString('es-ES')}
          </p>
        </div>
        <Button 
          onClick={fetchStats} 
          disabled={isLoading}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Actualizar
        </Button>
      </div>

      {/* Grid de estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className={`border-l-4 hover:shadow-lg transition-all duration-300 ${getColorClasses(stat.color)}`}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-2 bg-white/80 rounded-lg shadow-sm">
                    <stat.icon className="h-6 w-6" />
                  </div>
                  {stat.title === "Tickets Críticos" && stats.tickets.critical > 0 && (
                    <Badge variant="destructive" className="animate-pulse">
                      ¡Urgente!
                    </Badge>
                  )}
                  {stat.title === "Solicitudes Pendientes" && stats.access_requests.pending > 0 && (
                    <Badge variant="secondary" className="animate-bounce">
                      Nuevas
                    </Badge>
                  )}
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium text-sm opacity-90">{stat.title}</h3>
                  <div className="flex items-baseline gap-2">
                    <span className="text-3xl font-bold">{stat.value}</span>
                    <span className="text-xs font-medium opacity-75">{stat.trend}</span>
                  </div>
                  <p className="text-xs opacity-70">{stat.subtitle}</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Alertas críticas */}
      {(stats.tickets.critical > 0 || stats.access_requests.high_priority > 0) && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-gradient-to-r from-red-50 to-orange-50 border-l-4 border-red-500 p-6 rounded-lg"
        >
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-6 w-6 text-red-600 animate-pulse" />
            <div>
              <h3 className="font-bold text-red-800">¡Atención Requerida!</h3>
              <p className="text-red-700">
                {stats.tickets.critical > 0 && `${stats.tickets.critical} tickets críticos pendientes. `}
                {stats.access_requests.high_priority > 0 && `${stats.access_requests.high_priority} solicitudes de alta prioridad.`}
              </p>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};
