
-- Enable RLS on tables if not already enabled
ALTER TABLE empleados ENABLE ROW LEVEL SECURITY;
ALTER TABLE plataformas_acceso ENABLE ROW LEVEL SECURITY;
ALTER TABLE empleado_accesos ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for empleados (allow all for now)
DROP POLICY IF EXISTS "Allow all operations on empleados" ON empleados;
CREATE POLICY "Allow all operations on empleados" ON empleados
FOR ALL USING (true);

-- Create RLS policies for plataformas_acceso (allow all for now)
DROP POLICY IF EXISTS "Allow all operations on plataformas_acceso" ON plataformas_acceso;
CREATE POLICY "Allow all operations on plataformas_acceso" ON plataformas_acceso
FOR ALL USING (true);

-- Create RLS policies for empleado_accesos (allow all for now)
DROP POLICY IF EXISTS "Allow all operations on empleado_accesos" ON empleado_accesos;
CREATE POLICY "Allow all operations on empleado_accesos" ON empleado_accesos
FOR ALL USING (true);

-- Insert Ford platforms into plataformas_acceso table (check if exists first)
INSERT INTO plataformas_acceso (nombre)
SELECT unnest(ARRAY[
  'GTAC Viewer',
  'OWS',
  'MLP',
  'DSR',
  'SAP',
  'DEALIS',
  'Microsoft Teams',
  'Cisco Finnesse - Skills',
  'Jabber',
  'Portal del Empleado',
  'Otra herramienta'
])
WHERE NOT EXISTS (
  SELECT 1 FROM plataformas_acceso WHERE nombre IN (
    'GTAC Viewer', 'OWS', 'MLP', 'DSR', 'SAP', 'DEALIS',
    'Microsoft Teams', 'Cisco Finnesse - Skills', 'Jabber',
    'Portal del Empleado', 'Otra herramienta'
  )
);

-- Create a notification table for tracking access requests
CREATE TABLE IF NOT EXISTS notificaciones_acceso (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  empleado_nombre TEXT NOT NULL,
  empleado_cdsid TEXT NOT NULL,
  region TEXT NOT NULL,
  plataformas_faltantes TEXT[] NOT NULL,
  descripcion TEXT,
  prioridad TEXT NOT NULL CHECK (prioridad IN ('alta', 'media', 'baja')),
  estado TEXT DEFAULT 'pendiente' CHECK (estado IN ('pendiente', 'en_proceso', 'completado', 'cancelado')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on notifications table
ALTER TABLE notificaciones_acceso ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for notifications (allow all for now)
DROP POLICY IF EXISTS "Allow all operations on notificaciones_acceso" ON notificaciones_acceso;
CREATE POLICY "Allow all operations on notificaciones_acceso" ON notificaciones_acceso
FOR ALL USING (true);

-- Create function to get missing platforms for an employee
CREATE OR REPLACE FUNCTION get_missing_platforms(empleado_cdsid_param TEXT)
RETURNS TABLE(plataforma_nombre TEXT) AS $$
BEGIN
  RETURN QUERY
  SELECT pa.nombre
  FROM plataformas_acceso pa
  LEFT JOIN empleado_accesos ea ON pa.id = ea.plataforma_id 
    AND ea.empleado_id = (SELECT id FROM empleados WHERE cdsid = empleado_cdsid_param)
  WHERE (ea.id IS NULL OR ea.estado != 'activo')
  ORDER BY pa.nombre;
END;
$$ LANGUAGE plpgsql;
