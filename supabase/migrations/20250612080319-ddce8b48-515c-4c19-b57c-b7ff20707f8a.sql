
-- Crear tabla para el sistema de ticketing
CREATE TABLE IF NOT EXISTS tickets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  numero_ticket TEXT UNIQUE NOT NULL DEFAULT 'TK-' || LPAD(nextval('ticket_number_seq')::text, 6, '0'),
  titulo TEXT NOT NULL,
  descripcion TEXT NOT NULL,
  estado TEXT NOT NULL DEFAULT 'abierto' CHECK (estado IN ('abierto', 'en_progreso', 'resuelto', 'cerrado')),
  prioridad TEXT NOT NULL DEFAULT 'media' CHECK (prioridad IN ('baja', 'media', 'alta', 'critica')),
  categoria TEXT NOT NULL DEFAULT 'general' CHECK (categoria IN ('accesos', 'hardware', 'software', 'red', 'general')),
  solicitante_nombre TEXT NOT NULL,
  solicitante_cdsid TEXT NOT NULL,
  solicitante_email TEXT NOT NULL,
  asignado_a TEXT,
  tiempo_resolucion INTEGER,
  comentarios JSONB DEFAULT '[]'::jsonb,
  archivos_adjuntos TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  closed_at TIMESTAMP WITH TIME ZONE
);

-- Crear secuencia para números de ticket si no existe
CREATE SEQUENCE IF NOT EXISTS ticket_number_seq START 1;

-- Crear tabla para inventario IT
CREATE TABLE IF NOT EXISTS inventario_it (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tipo_equipo TEXT NOT NULL,
  marca TEXT NOT NULL,
  modelo TEXT NOT NULL,
  numero_serie TEXT UNIQUE NOT NULL,
  estado TEXT NOT NULL DEFAULT 'disponible' CHECK (estado IN ('disponible', 'asignado', 'mantenimiento', 'baja')),
  condicion TEXT NOT NULL DEFAULT 'nuevo' CHECK (condicion IN ('nuevo', 'bueno', 'regular', 'malo')),
  ubicacion TEXT NOT NULL,
  asignado_a_cdsid TEXT,
  asignado_a_nombre TEXT,
  fecha_asignacion TIMESTAMP WITH TIME ZONE,
  fecha_compra TIMESTAMP WITH TIME ZONE NOT NULL,
  garantia_hasta TIMESTAMP WITH TIME ZONE,
  notas TEXT,
  costo DECIMAL(10,2),
  proveedor TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Habilitar RLS en las nuevas tablas
ALTER TABLE tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventario_it ENABLE ROW LEVEL SECURITY;

-- Crear políticas RLS (permitir todo por ahora)
CREATE POLICY "Allow all operations on tickets" ON tickets FOR ALL USING (true);
CREATE POLICY "Allow all operations on inventario_it" ON inventario_it FOR ALL USING (true);

-- Crear triggers para updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tickets_updated_at BEFORE UPDATE ON tickets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_inventario_it_updated_at BEFORE UPDATE ON inventario_it
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insertar algunos datos de ejemplo para el inventario
INSERT INTO inventario_it (tipo_equipo, marca, modelo, numero_serie, ubicacion, fecha_compra, costo, proveedor) VALUES
('Laptop', 'Dell', 'Latitude 5520', '*********', 'Almacén Madrid', '2024-01-15', 850.00, 'Dell España'),
('Monitor', 'LG', '27UK850-W', '*********', 'Almacén Madrid', '2024-02-10', 320.00, 'LG Electronics'),
('Teléfono IP', 'Cisco', '7841', '*********', 'Almacén Barcelona', '2024-01-20', 180.00, 'Cisco Systems'),
('Laptop', 'HP', 'EliteBook 840', '*********', 'Asignado', '2023-12-05', 920.00, 'HP España'),
('Tablet', 'iPad', 'Air 5th Gen', '*********', 'Almacén Lisboa', '2024-03-01', 650.00, 'Apple Portugal');

-- Actualizar algunos equipos como asignados
UPDATE inventario_it SET 
    estado = 'asignado', 
    asignado_a_cdsid = 'MSX001', 
    asignado_a_nombre = 'Ana García',
    fecha_asignacion = NOW() - INTERVAL '30 days'
WHERE numero_serie = '*********';
