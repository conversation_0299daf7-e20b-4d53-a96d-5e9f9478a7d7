
-- Create admin users table for authentication
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'admin' CHECK (role IN ('admin', 'superadmin')),
  active BOOLEAN DEFAULT true,
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Update the existing tickets table to match our new schema
ALTER TABLE tickets 
  DROP COLUMN IF EXISTS numero_ticket,
  DROP COLUMN IF EXISTS titulo,
  DROP COLUMN IF EXISTS descripcion,
  DROP COLUMN IF EXISTS estado,
  DROP COLUMN IF EXISTS prioridad,
  DROP COLUMN IF EXISTS categoria,
  DROP COLUMN IF EXISTS solicitante_nombre,
  DROP COLUMN IF EXISTS solicitante_cdsid,
  DROP COLUMN IF EXISTS solicitante_email,
  DROP COLUMN IF EXISTS asignado_a,
  DROP COLUMN IF EXISTS tiempo_resolucion,
  DROP COLUMN IF EXISTS comentarios,
  DROP COLUMN IF EXISTS archivos_adjuntos,
  DROP COLUMN IF EXISTS closed_at;

-- Add new columns to tickets table
ALTER TABLE tickets 
  ADD COLUMN IF NOT EXISTS title TEXT NOT NULL DEFAULT '',
  ADD COLUMN IF NOT EXISTS description TEXT NOT NULL DEFAULT '',
  ADD COLUMN IF NOT EXISTS status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
  ADD COLUMN IF NOT EXISTS priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  ADD COLUMN IF NOT EXISTS category TEXT NOT NULL DEFAULT 'general' CHECK (category IN ('access', 'hardware', 'software', 'network', 'general')),
  ADD COLUMN IF NOT EXISTS creator_email TEXT NOT NULL DEFAULT '',
  ADD COLUMN IF NOT EXISTS creator_id TEXT NOT NULL DEFAULT '',
  ADD COLUMN IF NOT EXISTS affected_cdsid TEXT,
  ADD COLUMN IF NOT EXISTS assigned_to TEXT,
  ADD COLUMN IF NOT EXISTS closed_at TIMESTAMP WITH TIME ZONE,
  ADD COLUMN IF NOT EXISTS resolution_time INTEGER,
  ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}',
  ADD COLUMN IF NOT EXISTS sla_due_date TIMESTAMP WITH TIME ZONE,
  ADD COLUMN IF NOT EXISTS escalated BOOLEAN DEFAULT false;

-- Create ticket comments table
CREATE TABLE IF NOT EXISTS ticket_comments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  ticket_id UUID REFERENCES tickets(id) ON DELETE CASCADE NOT NULL,
  author_name TEXT NOT NULL,
  author_email TEXT NOT NULL,
  content TEXT NOT NULL,
  is_internal BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create ticket attachments table
CREATE TABLE IF NOT EXISTS ticket_attachments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  ticket_id UUID REFERENCES tickets(id) ON DELETE CASCADE NOT NULL,
  filename TEXT NOT NULL,
  file_url TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  uploaded_by TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create ticket history table for audit trail
CREATE TABLE IF NOT EXISTS ticket_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  ticket_id UUID REFERENCES tickets(id) ON DELETE CASCADE NOT NULL,
  action TEXT NOT NULL,
  old_value TEXT,
  new_value TEXT,
  changed_by TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS on new tables
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_history ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (allow all for now - will be refined later)
CREATE POLICY "Allow all operations on admin_users" ON admin_users FOR ALL USING (true);
CREATE POLICY "Allow all operations on ticket_comments" ON ticket_comments FOR ALL USING (true);
CREATE POLICY "Allow all operations on ticket_attachments" ON ticket_attachments FOR ALL USING (true);
CREATE POLICY "Allow all operations on ticket_history" ON ticket_history FOR ALL USING (true);

-- Create triggers for updated_at
CREATE TRIGGER update_admin_users_updated_at BEFORE UPDATE ON admin_users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample admin user (password should be hashed in real implementation)
INSERT INTO admin_users (email, password_hash, name, role) VALUES
('<EMAIL>', '$2b$10$example_hash', 'Admin MSX Valencia', 'admin')
ON CONFLICT (email) DO NOTHING;

-- Create function to generate ticket numbers
CREATE OR REPLACE FUNCTION generate_ticket_number()
RETURNS TEXT AS $$
DECLARE
    next_num INTEGER;
    ticket_num TEXT;
BEGIN
    -- Get next sequence value
    SELECT COALESCE(MAX(CAST(SUBSTRING(id FROM 4) AS INTEGER)), 0) + 1 
    INTO next_num 
    FROM tickets 
    WHERE id LIKE 'TK-%';
    
    -- Format as TK-XXXXXX
    ticket_num := 'TK-' || LPAD(next_num::TEXT, 6, '0');
    
    RETURN ticket_num;
END;
$$ LANGUAGE plpgsql;

-- Create function to set SLA due date based on priority
CREATE OR REPLACE FUNCTION set_sla_due_date()
RETURNS TRIGGER AS $$
BEGIN
    CASE NEW.priority
        WHEN 'critical' THEN
            NEW.sla_due_date := NEW.created_at + INTERVAL '4 hours';
        WHEN 'high' THEN
            NEW.sla_due_date := NEW.created_at + INTERVAL '24 hours';
        WHEN 'medium' THEN
            NEW.sla_due_date := NEW.created_at + INTERVAL '72 hours';
        WHEN 'low' THEN
            NEW.sla_due_date := NEW.created_at + INTERVAL '1 week';
    END CASE;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically set SLA due date
CREATE TRIGGER set_ticket_sla_due_date
    BEFORE INSERT ON tickets
    FOR EACH ROW
    EXECUTE FUNCTION set_sla_due_date();
